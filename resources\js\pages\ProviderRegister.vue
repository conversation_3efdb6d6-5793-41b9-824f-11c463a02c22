<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Head } from '@inertiajs/vue3';
import { ref, reactive, onMounted, computed } from 'vue';
import axios from 'axios';
import { useNotifications } from '@/composables/useNotifications';

// Props from the controller
const props = defineProps({
    clinics: {
        type: Array,
        default: () => []
    },
    specializations: {
        type: Array,
        default: () => []
    },
    languages: {
        type: Array,
        default: () => []
    }
});

const form = reactive({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    phone: '',
    gender: '',
    country_code: '', // User must select country
    clinic_id: '', // Selected clinic
    specialization: '',
    license_number: '',
    bio: '',
    languages: [],
});

const errors = ref({});
const showPassword = ref(false);
const showPasswordConfirmation = ref(false);
const isLoading = ref(false);

// Countries data
const countries = ref([]);
const countriesLoading = ref(false);

// Fetch countries from API
const fetchCountries = async () => {
    try {
        countriesLoading.value = true;
        const response = await axios.get('/api/countries');
        if (response.data.success) {
            countries.value = response.data.data;
        }
    } catch (error) {
        console.error('Error fetching countries:', error);
    } finally {
        countriesLoading.value = false;
    }
};

// Initialize notifications
const { showSuccess, showError } = useNotifications();

// Computed properties for validation
const isPasswordValid = computed(() => {
    const password = form.password;
    return password.length >= 8 &&
           /[A-Z]/.test(password) &&
           /[a-z]/.test(password) &&
           /[0-9]/.test(password);
});

const isBioValid = computed(() => {
    return form.bio.length >= 50 && form.bio.length <= 1000;
});

const submit = async () => {
    isLoading.value = true;
    errors.value = {};

    try {
        console.log('Submitting form data:', form);

        // Submit to API endpoint using axios
        const response = await axios.post('/provider/register', form, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
        });

        console.log('Response:', response.data);

        if (response.data.success) {
            showSuccess('Registration submitted successfully! You will receive an email confirmation within 24 hours.');
            // Reset form
            Object.keys(form).forEach(key => {
                if (key === 'languages') {
                    form[key] = [];
                } else {
                    form[key] = '';
                }
            });
        }
    } catch (error) {
        console.error('Full error object:', error);
        if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Error status:', error.response.status);

            if (error.response.status === 422) {
                // Validation errors
                errors.value = error.response.data.errors || {};
                console.error('Validation errors:', errors.value);
                console.error('Debug info:', error.response.data.debug);

                // Show a summary of validation errors
                const errorMessages = Object.values(errors.value).flat();
                if (errorMessages.length > 0) {
                    showError('Validation errors: ' + errorMessages.join('; '));
                }
            } else {
                showError(`Error ${error.response.status}: ${error.response.data.message || 'An error occurred during registration.'}`);
            }
        } else if (error.request) {
            console.error('No response received:', error.request);
            showError('No response from server. Please check your connection and try again.');
        } else {
            console.error('Request setup error:', error.message);
            showError('An error occurred setting up the request. Please try again.');
        }
    } finally {
        isLoading.value = false;
    }
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const togglePasswordConfirmationVisibility = () => {
    showPasswordConfirmation.value = !showPasswordConfirmation.value;
};

const handleLanguageChange = (language: string) => {
    const index = form.languages.indexOf(language);
    if (index > -1) {
        form.languages.splice(index, 1);
    } else {
        form.languages.push(language);
    }
};

// Initialize on mount
onMounted(() => {
    fetchCountries();
});
</script>

<template>
    <Head title="Provider Registration" />

    <div class="min-h-screen bg-medroid-sage py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-medroid-navy mb-4">
                    Join Our Provider Network
                </h1>
                <p class="text-lg text-medroid-slate max-w-2xl mx-auto">
                    Become part of Medroid's healthcare network and help provide quality care to patients worldwide.
                </p>
            </div>

            <!-- Registration Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <form @submit.prevent="submit" class="space-y-6">
                    <!-- Personal Information Section -->
                    <div class="border-b border-gray-200 pb-6">
                        <h2 class="text-xl font-semibold text-medroid-navy mb-4">Personal Information</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-medroid-navy mb-2">Full Name *</label>
                                <input
                                    id="name"
                                    v-model="form.name"
                                    type="text"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                    placeholder="Enter your full name"
                                />
                                <InputError class="mt-2" :message="errors.name?.[0]" />
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-medroid-navy mb-2">Email Address *</label>
                                <input
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                    placeholder="Enter your email address"
                                />
                                <InputError class="mt-2" :message="errors.email?.[0]" />
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-medroid-navy mb-2">Phone Number *</label>
                                <input
                                    id="phone"
                                    v-model="form.phone"
                                    type="tel"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                    placeholder="Enter your phone number"
                                />
                                <InputError class="mt-2" :message="errors.phone?.[0]" />
                            </div>

                            <!-- Gender -->
                            <div>
                                <label class="block text-sm font-medium text-medroid-navy mb-2">Gender *</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input
                                            v-model="form.gender"
                                            type="radio"
                                            value="male"
                                            class="mr-2 text-medroid-orange focus:ring-medroid-orange"
                                        />
                                        Male
                                    </label>
                                    <label class="flex items-center">
                                        <input
                                            v-model="form.gender"
                                            type="radio"
                                            value="female"
                                            class="mr-2 text-medroid-orange focus:ring-medroid-orange"
                                        />
                                        Female
                                    </label>
                                    <label class="flex items-center">
                                        <input
                                            v-model="form.gender"
                                            type="radio"
                                            value="other"
                                            class="mr-2 text-medroid-orange focus:ring-medroid-orange"
                                        />
                                        Other
                                    </label>
                                </div>
                                <InputError class="mt-2" :message="errors.gender?.[0]" />
                            </div>

                            <!-- Country -->
                            <div>
                                <label class="block text-sm font-medium text-medroid-navy mb-2">Country *</label>
                                <select
                                    v-model="form.country_code"
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent bg-white"
                                    required
                                    :disabled="countriesLoading"
                                >
                                    <option value="">{{ countriesLoading ? 'Loading countries...' : 'Select Country' }}</option>
                                    <option
                                        v-for="country in countries"
                                        :key="country.code"
                                        :value="country.code"
                                    >
                                        {{ country.name }}
                                    </option>
                                </select>
                                <InputError class="mt-2" :message="errors.country_code?.[0]" />
                            </div>
                        </div>
                    </div>

                    <!-- Account Security Section -->
                    <div class="border-b border-gray-200 pb-6">
                        <h2 class="text-xl font-semibold text-medroid-navy mb-4">Account Security</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Password -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-medroid-navy mb-2">Password *</label>
                                <div class="relative">
                                    <input
                                        id="password"
                                        v-model="form.password"
                                        :type="showPassword ? 'text' : 'password'"
                                        required
                                        class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                        placeholder="Min 8 chars, uppercase, lowercase, numbers"
                                    />
                                    <button
                                        type="button"
                                        @click="togglePasswordVisibility"
                                        class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"
                                    >
                                        <svg v-if="!showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <InputError class="mt-2" :message="errors.password?.[0]" />
                                <div class="mt-1 text-xs">
                                    <p class="text-gray-500 mb-1">Password requirements:</p>
                                    <div class="space-y-1">
                                        <div class="flex items-center">
                                            <span :class="form.password.length >= 8 ? 'text-green-600' : 'text-gray-400'">✓</span>
                                            <span class="ml-1" :class="form.password.length >= 8 ? 'text-green-600' : 'text-gray-500'">At least 8 characters</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span :class="/[A-Z]/.test(form.password) ? 'text-green-600' : 'text-gray-400'">✓</span>
                                            <span class="ml-1" :class="/[A-Z]/.test(form.password) ? 'text-green-600' : 'text-gray-500'">Uppercase letter</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span :class="/[a-z]/.test(form.password) ? 'text-green-600' : 'text-gray-400'">✓</span>
                                            <span class="ml-1" :class="/[a-z]/.test(form.password) ? 'text-green-600' : 'text-gray-500'">Lowercase letter</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span :class="/[0-9]/.test(form.password) ? 'text-green-600' : 'text-gray-400'">✓</span>
                                            <span class="ml-1" :class="/[0-9]/.test(form.password) ? 'text-green-600' : 'text-gray-500'">At least one number</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-medroid-navy mb-2">Confirm Password *</label>
                                <div class="relative">
                                    <input
                                        id="password_confirmation"
                                        v-model="form.password_confirmation"
                                        :type="showPasswordConfirmation ? 'text' : 'password'"
                                        required
                                        class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                        placeholder="Confirm your password"
                                    />
                                    <button
                                        type="button"
                                        @click="togglePasswordConfirmationVisibility"
                                        class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"
                                    >
                                        <svg v-if="!showPasswordConfirmation" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                                <InputError class="mt-2" :message="errors.password_confirmation?.[0]" />
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information Section -->
                    <div class="border-b border-gray-200 pb-6">
                        <h2 class="text-xl font-semibold text-medroid-navy mb-4">Professional Information</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Clinic Selection -->
                            <div>
                                <label for="clinic_id" class="block text-sm font-medium text-medroid-navy mb-2">Clinic *</label>
                                <select
                                    id="clinic_id"
                                    v-model="form.clinic_id"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                >
                                    <option value="">Select your clinic</option>
                                    <option v-for="clinic in clinics" :key="clinic.id" :value="clinic.id">
                                        {{ clinic.name }} - {{ clinic.location }}
                                    </option>
                                </select>
                                <InputError class="mt-2" :message="errors.clinic_id?.[0]" />
                            </div>

                            <!-- Specialization -->
                            <div>
                                <label for="specialization" class="block text-sm font-medium text-medroid-navy mb-2">Specialization *</label>
                                <select
                                    id="specialization"
                                    v-model="form.specialization"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                >
                                    <option value="">Select your specialization</option>
                                    <option v-for="spec in specializations" :key="spec" :value="spec">{{ spec }}</option>
                                </select>
                                <InputError class="mt-2" :message="errors.specialization?.[0]" />
                            </div>

                            <!-- License Number -->
                            <div>
                                <label for="license_number" class="block text-sm font-medium text-medroid-navy mb-2">License Number *</label>
                                <input
                                    id="license_number"
                                    v-model="form.license_number"
                                    type="text"
                                    required
                                    class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                    placeholder="Enter your medical license number"
                                />
                                <InputError class="mt-2" :message="errors.license_number?.[0]" />
                            </div>


                        </div>

                        <!-- Bio -->
                        <div class="mt-6">
                            <label for="bio" class="block text-sm font-medium text-medroid-navy mb-2">Professional Bio *</label>
                            <textarea
                                id="bio"
                                v-model="form.bio"
                                rows="4"
                                required
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"
                                placeholder="Tell us about your professional background, experience, and approach to patient care (minimum 50 characters)"
                            ></textarea>
                            <div class="flex justify-between items-center mt-1">
                                <p class="text-sm text-medroid-slate">Minimum 50 characters, maximum 1000 characters</p>
                                <p class="text-xs" :class="form.bio.length >= 50 ? 'text-green-600' : 'text-orange-500'">
                                    {{ form.bio.length }}/1000 characters
                                    <span v-if="form.bio.length < 50" class="text-orange-500">({{ 50 - form.bio.length }} more needed)</span>
                                </p>
                            </div>
                            <InputError class="mt-2" :message="errors.bio?.[0]" />
                        </div>

                        <!-- Languages -->
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-medroid-navy mb-2">Languages Spoken</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <label v-for="language in languages" :key="language" class="flex items-center">
                                    <input
                                        type="checkbox"
                                        :value="language"
                                        :checked="form.languages.includes(language)"
                                        @change="handleLanguageChange(language)"
                                        class="mr-2 text-medroid-orange focus:ring-medroid-orange"
                                    />
                                    {{ language }}
                                </label>
                            </div>
                            <InputError class="mt-2" :message="errors.languages?.[0]" />
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-6">
                        <button
                            type="submit"
                            :disabled="isLoading"
                            class="w-full bg-medroid-orange hover:bg-medroid-orange/90 disabled:bg-medroid-orange/50 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center"
                        >
                            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ isLoading ? 'Submitting Application...' : 'Submit Application' }}
                        </button>
                    </div>

                    <!-- Terms and Privacy -->
                    <div class="text-center pt-4">
                        <p class="text-sm text-medroid-slate">
                            By submitting this application, you agree to our
                            <a href="/policies" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">Terms of Service</a>
                            and
                            <a href="/policies" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">Privacy Policy</a>
                            • <a href="/policies" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">View All Policies</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>
