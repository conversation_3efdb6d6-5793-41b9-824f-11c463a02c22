<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Head, Link, useForm, router } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

// Props from the controller
const props = defineProps({
    canResetPassword: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        default: null,
    },
    isLoginMode: {
        type: Boolean,
        default: false,
    },
    waitlistStatus: {
        type: Object,
        default: () => ({ enabled: false, messages: {} }),
    },
    invitationToken: {
        type: String,
        default: null,
    },
    invitationValid: {
        type: Boolean,
        default: false,
    },
    invitation: {
        type: Object,
        default: null,
    },
    stripePublicKey: {
        type: String,
        default: '',
    },
});

const form = useForm({
    name: '',
    email: props.invitation?.email || '',
    password: '',
    password_confirmation: '',
    role: 'patient', // Always patient for signup
    gender: '',
    date_of_birth: '',
    country_code: '', // User must select country
    referral_code: '',
    invitation_token: props.invitationToken || '',
    selected_plan: 'free', // Default to free plan
});

// Note: Removed multi-step registration flow for simplified UX

// Note: Subscription plans and fetchSubscriptionPlans removed since plan selection step is removed

// Get selected plan details (simplified since we get plan info from URL)
const selectedPlan = computed(() => {
    // This could be enhanced to fetch plan details from API when needed
    return {
        slug: form.selected_plan,
        name: form.selected_plan === 'free' ? 'Free Plan' : 'Premium Plan',
        description: form.selected_plan === 'free' ? 'Start with our free features' : 'Unlock all premium features',
        formatted_price: form.selected_plan === 'free' ? 'Free' : '£29.99',
        interval: form.selected_plan === 'free' ? '' : 'month'
    };
});

// Reactive waitlist status (will be updated from API if props are not available)
const waitlistStatus = ref(props.waitlistStatus || { enabled: false, messages: {} });

// Countries data
const countries = ref([]);
const countriesLoading = ref(false);

// Fetch countries from API
const fetchCountries = async () => {
    try {
        countriesLoading.value = true;
        const response = await axios.get('/countries-list');
        if (response.data.success) {
            countries.value = response.data.data;
        }
    } catch (error) {
        console.error('Error fetching countries:', error);
    } finally {
        countriesLoading.value = false;
    }
};

// Fetch waitlist status from API if not provided by props
const fetchWaitlistStatus = async () => {
    try {
        console.log('Fetching waitlist status from API...');
        const response = await axios.get('/waitlist/status');
        waitlistStatus.value = response.data;
        console.log('Waitlist status fetched:', response.data);
        console.log('Waitlist enabled:', response.data.enabled);
    } catch (error) {
        console.error('Error fetching waitlist status:', error);
        // Keep default values
    }
};

const showPassword = ref(false);
const showPasswordConfirmation = ref(false);
const isLoading = ref(false);
const isLoginMode = ref(props.isLoginMode); // Toggle between login and register

// Multi-step flow state
const currentStep = ref(1); // 1: Registration, 2: Payment
const registrationData = ref<any>(null);
const stripe = ref<any>(null);
const cardElement = ref<any>(null);

// Payment form for step 2
const paymentForm = useForm({
    payment_method_id: '',
    billing_address: '',
    billing_city: '',
    billing_postal_code: '',
});

// Chat demo animation
const visibleMessages = ref([]);
const allMessages = [
    { type: 'user', text: 'I have been feeling tired lately and having headaches. What could be the cause?' },
    { type: 'bot', text: 'I understand your concern. Fatigue and headaches can have various causes. Can you tell me more about when these symptoms started and if you\'ve noticed any patterns?' },
    { type: 'user', text: 'It started about a week ago, mostly in the afternoons.' },
    { type: 'bot', text: 'Based on your symptoms, this could be related to dehydration, stress, or sleep patterns. I recommend drinking more water, ensuring adequate sleep, and monitoring your symptoms. If they persist, please consult with a healthcare provider.' },
    { type: 'user', text: 'Thank you! Should I be concerned about anything specific?' },
    { type: 'bot', text: 'Monitor for severe headaches, fever, or vision changes. These would require immediate medical attention. For now, focus on hydration and rest. Feel better soon!' }
];
// Animation functions
const startChatAnimation = () => {
    visibleMessages.value = [];
    let currentIndex = 0;

    const showNextMessage = () => {
        if (currentIndex < allMessages.length) {
            visibleMessages.value.push(allMessages[currentIndex]);
            currentIndex++;

            // Delay between messages (2 seconds for bot, 1.5 seconds for user)
            const delay = allMessages[currentIndex - 1]?.type === 'bot' ? 2000 : 1500;
            setTimeout(showNextMessage, delay);
        } else {
            // Restart animation after all messages are shown
            setTimeout(() => {
                startChatAnimation();
            }, 3000);
        }
    };

    // Start with first message after a short delay
    setTimeout(showNextMessage, 1000);
};

const submit = () => {
    if (isLoginMode.value) {
        // Handle login normally
        isLoading.value = true;
        form.post(route('login'), {
            onFinish: () => {
                form.reset('password');
                isLoading.value = false;
            },
        });
    } else {
        // Handle registration with multi-step flow
        if (form.selected_plan !== 'free' && currentStep.value === 1) {
            // For premium plans, go to payment step first
            registrationData.value = { ...form.data() };
            currentStep.value = 2;
            initializeStripe();
        } else {
            // For free plans or final step, submit registration
            isLoading.value = true;
            
            // Ensure fresh CSRF token before registration attempt
            const csrfToken = document.head.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            console.log('Current CSRF token:', csrfToken ? 'Present' : 'Missing');
            console.log('Attempting registration with data:', form.data());
            
            form.post(route('register'), {
                onFinish: () => {
                    form.reset('password', 'password_confirmation');
                    isLoading.value = false;
                },
                onError: (errors) => {
                    console.error('Registration failed with errors:', errors);
                    isLoading.value = false;
                },
                onSuccess: (page) => {
                    console.log('Registration successful:', page);
                },
            });
        }
    }
};

// Stripe initialization for payment step
const initializeStripe = async () => {
    try {
        // Load Stripe.js
        const script = document.createElement('script');
        script.src = 'https://js.stripe.com/v3/';
        script.onload = () => {
            stripe.value = (window as any).Stripe(props.stripePublicKey);
            const elements = stripe.value.elements();

            // Create card element with better styling (same as checkout page)
            cardElement.value = elements.create('card', {
                style: {
                    base: {
                        fontSize: '16px',
                        color: '#1f2937',
                        fontFamily: 'system-ui, -apple-system, sans-serif',
                        fontSmoothing: 'antialiased',
                        '::placeholder': {
                            color: '#9ca3af',
                        },
                        iconColor: '#6b7280',
                    },
                    invalid: {
                        color: '#ef4444',
                        iconColor: '#ef4444',
                    },
                },
                hidePostalCode: true, // We collect this separately
            });
            
            // Wait for DOM to be ready and mount card element
            setTimeout(() => {
                const cardElementDiv = document.getElementById('card-element');
                if (cardElementDiv) {
                    cardElement.value.mount('#card-element');
                } else {
                    console.error('Card element container not found');
                }
            }, 100);
        };
        document.head.appendChild(script);
    } catch (err) {
        console.error('Failed to load Stripe:', err);
    }
};

// Process payment and create account
const processPayment = async () => {
    if (!stripe.value || !cardElement.value) {
        alert('Payment system not ready');
        return;
    }

    isLoading.value = true;

    try {
        // Create payment method
        const { error: stripeError, paymentMethod } = await stripe.value.createPaymentMethod({
            type: 'card',
            card: cardElement.value,
            billing_details: {
                name: registrationData.value.name,
                email: registrationData.value.email,
                address: {
                    line1: paymentForm.billing_address,
                    city: paymentForm.billing_city,
                    postal_code: paymentForm.billing_postal_code,
                },
            },
        });

        if (stripeError) {
            alert(stripeError.message);
            isLoading.value = false;
            return;
        }

        // Combine registration data with payment data and submit using Inertia
        const finalForm = useForm({
            ...registrationData.value,
            payment_method_id: paymentMethod.id,
            billing_address: paymentForm.billing_address,
            billing_city: paymentForm.billing_city,
            billing_postal_code: paymentForm.billing_postal_code,
        });

        // Submit to the new endpoint that handles both registration and payment
        finalForm.post('/register-with-payment', {
            onError: (errors) => {
                console.error('Registration with payment failed:', errors);
                alert(errors.payment || 'Payment failed. Please try again.');
                isLoading.value = false;
            },
            onSuccess: () => {
                isLoading.value = false;
                // Success will be handled by redirect from backend
            },
        });

    } catch (error) {
        console.error('Payment failed:', error);
        alert('Payment failed. Please try again.');
        isLoading.value = false;
    }
};

// Go back to registration step
const goBackToRegistration = () => {
    currentStep.value = 1;
};

const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value;
};

const togglePasswordConfirmationVisibility = () => {
    showPasswordConfirmation.value = !showPasswordConfirmation.value;
};

const toggleMode = () => {
    if (isLoginMode.value) {
        // If user is in login mode and wants to sign up, redirect to membership page
        router.visit('/membership');
    } else {
        // If user is in register mode and wants to sign in, switch to login mode
        isLoginMode.value = true;
        form.reset();
    }
};

// Note: Removed step navigation since we're using a simple form now

// Note: Removed step validation since we're using a simple form now

// Start chat animation when component mounts
onMounted(() => {
    startChatAnimation();

    // Fetch countries
    fetchCountries();

    // Skip fetching subscription plans since we removed plan selection step

    console.log('Component mounted. Props waitlistStatus:', props.waitlistStatus);

    // Check for selected_plan URL parameter (only in register mode)
    if (!isLoginMode.value) {
        const urlParams = new URLSearchParams(window.location.search);
        const selectedPlanParam = urlParams.get('selected_plan');
        const planTypeParam = urlParams.get('plan_type');
        
        if (selectedPlanParam) {
            form.selected_plan = selectedPlanParam;
            // Plan is pre-selected from membership page
        }
    }

    // Always fetch waitlist status to ensure we have the latest
    fetchWaitlistStatus();
});
</script>

<template>
    <Head :title="isLoginMode ? 'Sign In' : 'Register'" />

    <div class="min-h-screen bg-medroid-sage flex">
        <!-- Left Panel - Auth Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-medroid-cream">
            <div class="max-w-md w-full">
                <!-- Header -->
                <div class="text-left mb-8">
                    <h1 class="text-4xl font-bold text-medroid-navy mb-2">
                        Your AI Doctor,
                    </h1>
                    <h2 class="text-4xl font-bold text-medroid-navy mb-4">
                        Always On Call
                    </h2>
                    <p class="text-medroid-slate mb-8">
                        AI-powered healthcare that puts your well-being first.
                    </p>
                </div>

                <!-- Google Sign In Button -->
                <a
                    :href="route('auth.google')"
                    :disabled="!isLoginMode && waitlistStatus.enabled && !invitationValid"
                    :class="[
                        'w-full flex items-center justify-center px-4 py-3 border border-medroid-border rounded-lg shadow-sm text-sm font-medium transition-colors duration-200 mb-6 no-underline',
                        (!isLoginMode && waitlistStatus.enabled && !invitationValid)
                            ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                            : 'text-medroid-navy bg-white hover:bg-medroid-sage',
                        'no-underline'
                    ]"
                >
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    {{ isLoginMode ? 'Continue with Google' :
                       (waitlistStatus.enabled && !invitationValid ? 'Google Sign-up Disabled' : 'Continue with Google') }}
                </a>

                <!-- Waitlist Notice for Google Sign-up -->
                <div v-if="!isLoginMode && waitlistStatus.enabled && !invitationValid" class="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p class="text-sm text-orange-700">
                        {{ waitlistStatus.messages.google_signin_disabled }}
                    </p>
                </div>

                <!-- Divider -->
                <div class="relative mb-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-medroid-border"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-medroid-cream text-medroid-slate">OR</span>
                    </div>
                </div>

                <!-- Status Message -->
                <div v-if="status" class="mb-4 text-center text-sm font-medium text-medroid-teal">
                    {{ status }}
                </div>

                <!-- Invitation Notice -->
                <div v-if="!isLoginMode && invitationValid && invitation" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-green-800 mb-1">🎉 Welcome to {{ invitation.club_info.club_name }}!</h3>
                            <p class="text-sm text-green-700">
                                You've been invited to join Medroid. Complete your registration below.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Waitlist Notice (when no valid invitation) -->
                <div v-else-if="!isLoginMode && waitlistStatus.enabled && !invitationValid" class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 mb-1">Invitation Required</h3>
                            <p class="text-sm text-blue-700">
                                {{ waitlistStatus.messages.signup_disabled }}
                            </p>
                            <p class="text-sm text-blue-600 mt-2">
                                <a href="/" class="underline hover:text-blue-800">Join our waitlist</a> to get an invitation!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Step 1: Registration Form -->
                <form v-if="!isLoginMode && currentStep === 1" class="space-y-5" @submit.prevent="submit">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-medroid-navy mb-2">Create Your Account</h3>
                        <p class="text-medroid-slate">Join Medroid and start your wellness journey</p>
                        
                        <!-- Plan Status Display -->
                        <div class="mt-4">
                            <!-- Premium Plan Selected -->
                            <div v-if="form.selected_plan !== 'free'" class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3 text-center">
                                <div class="flex items-center justify-center mb-1">
                                    <svg class="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="text-sm font-semibold text-blue-800">{{ selectedPlan.name }} Selected</span>
                                </div>
                                <p class="text-xs text-blue-700 mb-2">Unlock your full potential with premium features</p>
                                <div class="text-base font-bold text-blue-900">{{ selectedPlan.formatted_price }}<span v-if="selectedPlan.interval" class="text-sm font-normal text-blue-700">/{{ selectedPlan.interval }}</span></div>
                            </div>

                            <!-- Free Plan - Upgrade CTA -->
                            <div v-else class="text-center">
                                <Link
                                    :href="route('membership.index')"
                                    class="inline-flex flex-col items-center px-6 py-4 rounded-xl text-sm font-medium bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 hover:from-orange-200 hover:to-red-200 transition-all duration-200 border border-orange-200"
                                >
                                    <div class="flex items-center mb-1">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                                        </svg>
                                        <span class="font-semibold">Unlock Full Potential</span>
                                    </div>
                                    <span class="text-xs text-orange-700">Get Premium Plan →</span>
                                </Link>
                            </div>
                        </div>
                    </div>

                    <!-- Name Field -->
                    <div>
                        <input
                            id="name"
                            v-model="form.name"
                            type="text"
                            placeholder="Full Name"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                            required
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.name" />
                    </div>

                    <!-- Email Field -->
                    <div>
                        <input
                            id="email"
                            v-model="form.email"
                            type="email"
                            placeholder="Email Address"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                            required
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <!-- Password Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password"
                                v-model="form.password"
                                :type="showPassword ? 'text' : 'password'"
                                placeholder="Password"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder pr-10 bg-white"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <button
                                type="button"
                                @click="togglePasswordVisibility"
                                class="absolute inset-y-0 right-3 flex items-center text-medroid-slate hover:text-medroid-navy"
                            >
                                <svg v-if="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L12 12m-2.122-2.122L7.758 7.758M12 12l2.122 2.122m-2.122-2.122L16.242 16.242M12 12l4.242 4.242M9.878 9.878l-2.12-2.121m4.242 4.242L14.121 14.121"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Password Confirmation Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password_confirmation"
                                v-model="form.password_confirmation"
                                :type="showPasswordConfirmation ? 'text' : 'password'"
                                placeholder="Confirm Password"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder pr-10 bg-white"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <button
                                type="button"
                                @click="togglePasswordConfirmationVisibility"
                                class="absolute inset-y-0 right-3 flex items-center text-medroid-slate hover:text-medroid-navy"
                            >
                                <svg v-if="showPasswordConfirmation" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L12 12m-2.122-2.122L7.758 7.758M12 12l2.122 2.122m-2.122-2.122L16.242 16.242M12 12l4.242 4.242M9.878 9.878l-2.12-2.121m4.242 4.242L14.121 14.121"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password_confirmation" />
                    </div>

                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Gender Field -->
                        <div>
                            <select
                                v-model="form.gender"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent bg-white appearance-none bg-no-repeat bg-right pr-10"
                                style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23666&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-position: right 12px center; background-size: 12px;"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            >
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                                <option value="prefer_not_to_say">Prefer not to say</option>
                            </select>
                            <InputError class="mt-2" :message="form.errors.gender" />
                        </div>

                        <!-- Date of Birth Field -->
                        <div>
                            <input
                                v-model="form.date_of_birth"
                                type="date"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent bg-white"
                                required
                                :disabled="waitlistStatus.enabled && !invitationValid"
                            />
                            <InputError class="mt-2" :message="form.errors.date_of_birth" />
                        </div>
                    </div>

                    <!-- Country Selection -->
                    <div>
                        <select
                            v-model="form.country_code"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent bg-white"
                            required
                            :disabled="waitlistStatus.enabled && !invitationValid || countriesLoading"
                        >
                            <option value="">{{ countriesLoading ? 'Loading countries...' : 'Select Country' }}</option>
                            <option
                                v-for="country in countries"
                                :key="country.code"
                                :value="country.code"
                            >
                                {{ country.name }}
                            </option>
                        </select>
                        <InputError class="mt-2" :message="form.errors.country_code" />
                    </div>

                    <!-- Referral Code Field -->
                    <div>
                        <input
                            v-model="form.referral_code"
                            type="text"
                            placeholder="Referral Code (Optional)"
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                            :disabled="waitlistStatus.enabled && !invitationValid"
                        />
                        <InputError class="mt-2" :message="form.errors.referral_code" />
                    </div>

                    <!-- Submit Button -->
                    <button
                        type="submit"
                        :disabled="isLoading || (waitlistStatus.enabled && !invitationValid)"
                        :class="[
                            'w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200',
                            isLoading || (waitlistStatus.enabled && !invitationValid) ? 'opacity-50 cursor-not-allowed' : ''
                        ]"
                    >
                        <span v-if="isLoading" class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Creating Account...
                        </span>
                        <span v-else>
                            {{ form.selected_plan === 'free' ? 'Create Free Account' : 'Create Account & Continue to Payment' }}
                        </span>
                    </button>

                    <!-- Footer Information -->
                    <div class="pt-4 text-center">
                        <p class="text-sm text-medroid-slate">
                            Already have an account?
                            <button
                                type="button"
                                @click="toggleMode"
                                class="font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline ml-1"
                            >
                                Sign in
                            </button>
                        </p>
                    </div>
                </form>

                <!-- Step 2: Payment Form -->
                <div v-if="!isLoginMode && currentStep === 2" class="space-y-6">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-medroid-navy mb-2">Complete Your Payment</h3>
                        <p class="text-medroid-slate">Secure payment for your {{ selectedPlan.name }}</p>

                        <!-- Plan Summary -->
                        <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-3 mt-4">
                            <div class="flex items-center justify-center mb-1">
                                <svg class="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm font-semibold text-blue-800">{{ selectedPlan.name }}</span>
                            </div>
                            <div class="text-base font-bold text-blue-900">{{ selectedPlan.formatted_price }}<span v-if="selectedPlan.interval" class="text-sm font-normal text-blue-700">/{{ selectedPlan.interval }}</span></div>
                        </div>
                    </div>

                    <!-- Account Summary -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-sm text-gray-700 mb-3">Account Information</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <span class="text-xs text-gray-600">Full Name</span>
                                <div class="text-sm font-medium text-gray-900">{{ registrationData?.name }}</div>
                            </div>
                            <div>
                                <span class="text-xs text-gray-600">Email Address</span>
                                <div class="text-sm font-medium text-gray-900">{{ registrationData?.email }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Address -->
                    <div class="space-y-4">
                        <h4 class="font-medium text-sm text-gray-700">Billing Address</h4>
                        <div>
                            <input
                                v-model="paymentForm.billing_address"
                                type="text"
                                placeholder="Address Line 1"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                                required
                            />
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <input
                                v-model="paymentForm.billing_city"
                                type="text"
                                placeholder="City"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                                required
                            />
                            <input
                                v-model="paymentForm.billing_postal_code"
                                type="text"
                                placeholder="Postal Code"
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-transparent placeholder-medroid-placeholder bg-white"
                                required
                            />
                        </div>
                    </div>

                    <!-- Card Element -->
                    <div class="space-y-2">
                        <h4 class="font-medium text-sm text-gray-700">Payment Information</h4>
                        <div id="card-element" class="w-full px-4 py-3 border border-medroid-border rounded-lg bg-white"></div>
                    </div>

                    <!-- Payment Buttons -->
                    <div class="space-y-3">
                        <button
                            type="button"
                            @click="processPayment"
                            :disabled="isLoading"
                            class="w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                        >
                            <span v-if="isLoading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing Payment...
                            </span>
                            <span v-else>
                                Complete Payment & Create Account
                            </span>
                        </button>

                        <button
                            type="button"
                            @click="goBackToRegistration"
                            class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                        >
                            ← Back to Registration
                        </button>
                    </div>
                </div>

                <!-- Login Form -->
                <form v-if="isLoginMode" class="space-y-5" @submit.prevent="submit">
                    <!-- Email Field -->
                    <div>
                        <input
                            id="email"
                            v-model="form.email"
                            name="email"
                            type="email"
                            autocomplete="email"
                            required
                            class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                            placeholder="Enter your personal or work email"
                        />
                        <InputError class="mt-2" :message="form.errors.email" />
                    </div>

                    <!-- Password Field -->
                    <div>
                        <div class="relative">
                            <input
                                id="password"
                                v-model="form.password"
                                name="password"
                                :type="showPassword ? 'text' : 'password'"
                                autocomplete="current-password"
                                required
                                class="w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200 text-medroid-navy placeholder-medroid-slate bg-white"
                                placeholder="Enter your password"
                            />
                            <button
                                type="button"
                                @click="togglePasswordVisibility"
                                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"
                            >
                                <svg v-if="showPassword" class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg v-else class="w-5 h-5 text-medroid-slate" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        <InputError class="mt-2" :message="form.errors.password" />
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button
                            type="submit"
                            :disabled="form.processing || isLoading"
                            class="w-full bg-medroid-orange hover:bg-medroid-orange/90 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                        >
                            {{ form.processing || isLoading ? 'Signing In...' : 'Sign In' }}
                        </button>
                    </div>

                    <!-- Toggle Mode Link -->
                    <div class="text-center pt-4">
                        <p class="text-sm text-medroid-slate">
                            Don't have an account?
                            <button
                                type="button"
                                @click="toggleMode"
                                class="font-medium text-medroid-orange hover:text-medroid-orange/80 transition-colors duration-200 underline"
                            >
                                Choose Plan
                            </button>
                        </p>
                    </div>

                    <!-- Forgot Password -->
                    <div v-if="canResetPassword" class="text-center">
                        <Link
                            :href="route('password.request')"
                            class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200 underline"
                        >
                            Forgot password?
                        </Link>
                    </div>
                </form>

                <!-- Privacy Policy - Fixed Position -->
                <div class="text-center mt-6">
                    <p class="text-xs text-medroid-slate">
                        By continuing, you acknowledge Medroid's
                        <a href="/policies" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">Privacy Policy</a>
                        • <a href="/policies" target="_blank" class="text-medroid-orange hover:text-medroid-orange/80 underline">All Policies</a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Right Panel - Chat Demo -->
        <div class="hidden lg:flex lg:w-1/2 bg-medroid-sage flex-col">
            <!-- Chat Header -->
            <div class="bg-white p-6 border-b border-medroid-border">
                <h3 class="text-xl font-semibold text-medroid-navy mb-2">Experience Medroid</h3>
                <p class="text-medroid-slate text-sm">Chat with our AI doctor for instant health insights</p>
            </div>

            <!-- Chat Messages -->
            <div class="flex-1 p-6 overflow-y-auto space-y-4 chat-container">
                <div
                    v-for="(message, index) in visibleMessages"
                    :key="index"
                    :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'"
                    class="animate-fade-in-up"
                    :style="{ animationDelay: `${index * 0.1}s` }"
                >
                    <div
                        :class="[
                            'message-bubble max-w-xs lg:max-w-md px-4 py-3 rounded-2xl text-sm',
                            message.type === 'user'
                                ? 'bg-medroid-orange text-white rounded-br-md shadow-lg'
                                : 'bg-white text-medroid-navy border border-medroid-border rounded-bl-md shadow-sm hover:shadow-md'
                        ]"
                    >
                        {{ message.text }}
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-6 bg-white border-t border-medroid-border">
                <div class="flex items-center space-x-3">
                    <input
                        type="text"
                        placeholder="Type your health question here..."
                        class="flex-1 px-4 py-3 border border-medroid-border rounded-full focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange text-sm text-medroid-navy placeholder-medroid-slate"
                        disabled
                    />
                    <button
                        class="bg-medroid-orange hover:bg-medroid-orange/90 text-white p-3 rounded-full transition-colors duration-200 disabled:opacity-50"
                        disabled
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>

                <!-- Learn More Link -->
                <div class="mt-4 text-center">
                    <a href="#" class="text-sm text-medroid-slate hover:text-medroid-orange transition-colors duration-200">
                        Learn more about Medroid →
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Chat message animations */
.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Typing indicator animation */
.typing-indicator {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

/* Smooth scroll for chat messages */
.chat-container {
    scroll-behavior: smooth;
}

/* Enhanced message bubble animations */
.message-bubble {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-bubble:hover {
    transform: translateY(-2px);
}
</style>