/**
 * Consultation API Composable
 * 
 * This composable provides methods for managing consultations, diagnoses,
 * treatment plans, and consultation notes.
 */

import { ref, type Ref } from 'vue'
import { useApi } from './useApi'

export interface Consultation {
  id: number
  appointment_id?: number
  patient_id: number
  provider_id: number
  clinic_id?: number
  consultation_type: string
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled'
  consultation_date: string
  duration_minutes?: number
  consultation_mode: 'in_person' | 'video' | 'phone'
  is_telemedicine: boolean
  vital_signs?: Record<string, any>
  main_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
  additional_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
  started_at?: string
  completed_at?: string
  archived?: boolean
  archived_at?: string
  created_at: string
  updated_at: string
  patient?: any
  provider?: any
  appointment?: any
  notes?: ConsultationNote[]
  diagnoses?: Diagnosis[]
  treatment_plans?: TreatmentPlan[]
  prescriptions?: any[]
  documents?: any[]
  medical_letters?: any[]
}

export interface ConsultationNote {
  id: number
  consultation_id: number
  created_by: number
  note_type: 'general' | 'follow_up' | 'amendment' | 'addendum'
  content: string
  attachments?: string[]
  is_private: boolean
  note_date: string
  created_at: string
  updated_at: string
  creator?: any
}

export interface Diagnosis {
  id: number
  consultation_id: number
  diagnosis_code?: string
  diagnosis_system?: string
  diagnosis_name: string
  description?: string
  type: 'primary' | 'secondary' | 'differential'
  status: 'active' | 'resolved' | 'chronic' | 'suspected'
  onset_date?: string
  resolved_date?: string
  notes?: string
  severity?: number
  created_at: string
  updated_at: string
}

export interface TreatmentPlan {
  id: number
  consultation_id: number
  plan_type: 'treatment' | 'investigation' | 'referral' | 'lifestyle'
  title: string
  description: string
  instructions?: string
  start_date?: string
  end_date?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'planned' | 'active' | 'completed' | 'cancelled' | 'on_hold'
  outcome?: string
  review_date?: string
  goals?: Record<string, any>
  created_at: string
  updated_at: string
}

export function useConsultationApi() {
  const api = useApi()
  const consultations: Ref<Consultation[]> = ref([])
  const currentConsultation: Ref<Consultation | null> = ref(null)

  // Consultation methods
  const getConsultations = async (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    const response = await api.get(`/consultations-list${queryString}`)
    if (response?.data) {
      consultations.value = response.data.data || response.data
    }
    return response
  }

  const getConsultation = async (id: number) => {
    const response = await api.get(`/consultations/${id}`)
    if (response?.data) {
      currentConsultation.value = response.data
    }
    return response
  }

  const createConsultation = async (data: Partial<Consultation>) => {
    const response = await api.post('/consultations', data)
    if (response?.data) {
      consultations.value.unshift(response.data)
      currentConsultation.value = response.data
    }
    return response
  }

  const updateConsultation = async (id: number, data: Partial<Consultation>) => {
    const response = await api.put(`/consultations/${id}`, data)
    if (response?.data) {
      const index = consultations.value.findIndex(c => c.id === id)
      if (index !== -1) {
        consultations.value[index] = response.data
      }
      if (currentConsultation.value?.id === id) {
        currentConsultation.value = response.data
      }
    }
    return response
  }

  const deleteConsultation = async (id: number) => {
    const success = await api.delete(`/consultations/${id}`)
    if (success) {
      consultations.value = consultations.value.filter(c => c.id !== id)
      if (currentConsultation.value?.id === id) {
        currentConsultation.value = null
      }
    }
    return success
  }

  const createFromAppointment = async (appointmentId: number) => {
    const response = await api.post(`/consultations/from-appointment/${appointmentId}`)
    if (response?.data) {
      consultations.value.unshift(response.data)
      currentConsultation.value = response.data
    }
    return response
  }

  // Consultation Notes methods
  const getConsultationNotes = async (consultationId: number, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    return await api.get(`/consultations/${consultationId}/notes${queryString}`)
  }

  const createConsultationNote = async (consultationId: number, data: Partial<ConsultationNote>) => {
    return await api.post(`/consultations/${consultationId}/notes`, data)
  }

  const updateConsultationNote = async (consultationId: number, noteId: number, data: Partial<ConsultationNote>) => {
    return await api.put(`/consultations/${consultationId}/notes/${noteId}`, data)
  }

  const deleteConsultationNote = async (consultationId: number, noteId: number) => {
    return await api.delete(`/consultations/${consultationId}/notes/${noteId}`)
  }

  // Diagnosis methods
  const getDiagnoses = async (consultationId: number, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    return await api.get(`/consultations/${consultationId}/diagnoses${queryString}`)
  }

  const createDiagnosis = async (consultationId: number, data: Partial<Diagnosis>) => {
    return await api.post(`/consultations/${consultationId}/diagnoses`, data)
  }

  const updateDiagnosis = async (consultationId: number, diagnosisId: number, data: Partial<Diagnosis>) => {
    return await api.put(`/consultations/${consultationId}/diagnoses/${diagnosisId}`, data)
  }

  const deleteDiagnosis = async (consultationId: number, diagnosisId: number) => {
    return await api.delete(`/consultations/${consultationId}/diagnoses/${diagnosisId}`)
  }

  const searchDiagnoses = async (search: string) => {
    return await api.get(`/diagnoses/search?search=${encodeURIComponent(search)}`)
  }

  // Treatment Plan methods
  const getTreatmentPlans = async (consultationId: number, params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : ''
    return await api.get(`/consultations/${consultationId}/treatment-plans${queryString}`)
  }

  const createTreatmentPlan = async (consultationId: number, data: Partial<TreatmentPlan>) => {
    return await api.post(`/consultations/${consultationId}/treatment-plans`, data)
  }

  const updateTreatmentPlan = async (consultationId: number, planId: number, data: Partial<TreatmentPlan>) => {
    return await api.put(`/consultations/${consultationId}/treatment-plans/${planId}`, data)
  }

  const deleteTreatmentPlan = async (consultationId: number, planId: number) => {
    return await api.delete(`/consultations/${consultationId}/treatment-plans/${planId}`)
  }

  const markTreatmentPlanCompleted = async (consultationId: number, planId: number, outcome?: string) => {
    return await api.post(`/consultations/${consultationId}/treatment-plans/${planId}/complete`, { outcome })
  }

  const getTreatmentPlansDueForReview = async () => {
    return await api.get('/treatment-plans/due-for-review')
  }

  // Archive methods
  const archiveConsultation = async (consultationId: number) => {
    return await api.post(`/consultations/${consultationId}/archive`)
  }

  const unarchiveConsultation = async (consultationId: number) => {
    return await api.post(`/consultations/${consultationId}/unarchive`)
  }

  return {
    // State
    consultations,
    currentConsultation,
    loading: api.loading,
    error: api.error,

    // Consultation methods
    getConsultations,
    getConsultation,
    createConsultation,
    updateConsultation,
    deleteConsultation,
    createFromAppointment,
    archiveConsultation,
    unarchiveConsultation,

    // Notes methods
    getConsultationNotes,
    createConsultationNote,
    updateConsultationNote,
    deleteConsultationNote,

    // Diagnosis methods
    getDiagnoses,
    createDiagnosis,
    updateDiagnosis,
    deleteDiagnosis,
    searchDiagnoses,

    // Treatment plan methods
    getTreatmentPlans,
    createTreatmentPlan,
    updateTreatmentPlan,
    deleteTreatmentPlan,
    markTreatmentPlanCompleted,
    getTreatmentPlansDueForReview,

    // Utility methods
    reset: api.reset,
    clearError: api.clearError
  }
}
