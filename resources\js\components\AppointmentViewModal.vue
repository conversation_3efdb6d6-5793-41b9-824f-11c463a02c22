<template>
    <!-- Appointment Details Modal -->
    <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-60 backdrop-blur-sm transition-opacity duration-300" @click="$emit('close')"></div>
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl transform transition-all duration-300 scale-100">
                <!-- Header -->
                <div class="bg-white border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <Icon name="calendar-check" class="text-teal-600 w-6 h-6 mr-3" />
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Appointment Details</h3>
                                <p class="text-sm text-gray-500">{{ currentAppointment?.confirmation_number || `#${currentAppointment?.id}` }}</p>
                            </div>
                        </div>
                        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 hover:bg-gray-100 rounded-lg">
                            <Icon name="x" class="w-5 h-5" />
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6 max-h-[70vh] overflow-y-auto">
                    <!-- Loading State -->
                    <div v-if="loading" class="flex items-center justify-center py-12">
                        <div class="flex items-center space-x-3">
                            <Icon name="loader-2" class="w-6 h-6 text-teal-600 animate-spin" />
                            <span class="text-gray-600">Loading appointment details...</span>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div v-else-if="error" class="flex items-center justify-center py-12">
                        <div class="text-center">
                            <Icon name="alert-circle" class="w-12 h-12 text-red-500 mx-auto mb-4" />
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Appointment</h3>
                            <p class="text-gray-600 mb-4">{{ error }}</p>
                            <button
                                @click="fetchAppointmentDetails"
                                class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                            >
                                Try Again
                            </button>
                        </div>
                    </div>

                    <!-- Appointment Details -->
                    <div v-else-if="currentAppointment" class="space-y-6">
                        <!-- Appointment Reason - Prominent Display -->
                        <div v-if="currentAppointment.reason" class="bg-gradient-to-r from-teal-50 to-teal-100 border border-teal-200 rounded-lg p-4 shadow-sm">
                            <div class="flex items-start">
                                <Icon name="stethoscope" class="text-teal-600 w-5 h-5 mr-3 mt-0.5" />
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-teal-900 mb-2 flex items-center">
                                        Reason for Appointment
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-teal-200 text-teal-800">
                                            Primary Concern
                                        </span>
                                    </h4>
                                    <p class="text-sm text-teal-800 leading-relaxed">{{ currentAppointment.reason }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Main Information Grid -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Basic Information -->
                            <div class="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <Icon name="info" class="text-blue-600 w-4 h-4 mr-2" />
                                    <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Basic Information</h4>
                                </div>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Service:</span>
                                        <span class="text-gray-900">{{ currentAppointment.service?.name || 'General Consultation' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Type:</span>
                                        <span :class="getTypeBadgeClass(currentAppointment.is_telemedicine)" class="inline-flex px-2 py-0.5 text-xs font-medium rounded-md">
                                            {{ currentAppointment.is_telemedicine ? 'Telemedicine' : 'In-Person' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Status:</span>
                                        <span :class="getStatusBadgeClass(currentAppointment.status)" class="inline-flex px-2 py-0.5 text-xs font-medium rounded-md">
                                            {{ formatStatus(currentAppointment.status) }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Check-in Status:</span>
                                        <span :class="getCheckInBadgeClass(appointment)" class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-md">
                                            <i :class="getCheckInIcon(appointment)" class="text-xs mr-1"></i>
                                            {{ getCheckInStatus(appointment) }}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Duration:</span>
                                        <span class="text-gray-900">{{ currentAppointment.duration_minutes || currentAppointment.service?.duration || 30 }} minutes</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Schedule Information -->
                            <div class="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <Icon name="calendar" class="text-purple-600 w-4 h-4 mr-2" />
                                    <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Schedule Information</h4>
                                </div>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Date:</span>
                                        <span class="text-gray-900">{{ formatDate(currentAppointment.scheduled_at || currentAppointment.date) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Time:</span>
                                        <span class="text-gray-900">{{ formatTime(currentAppointment.scheduled_at || currentAppointment.time_slot) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Scheduled:</span>
                                        <span class="text-gray-900">{{ formatDateTime(currentAppointment.created_at) }}</span>
                                    </div>
                                    <div v-if="currentAppointment.checked_in_at" class="flex justify-between">
                                        <span class="font-medium text-gray-700">Checked In:</span>
                                        <span class="text-gray-900">{{ formatDateTime(currentAppointment.checked_in_at) }}</span>
                                    </div>
                                    <div v-if="currentAppointment.checked_out_at" class="flex justify-between">
                                        <span class="font-medium text-gray-700">Checked Out:</span>
                                        <span class="text-gray-900">{{ formatDateTime(currentAppointment.checked_out_at) }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Patient Information -->
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <Icon name="user" class="text-blue-600 w-4 h-4 mr-2" />
                                    <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Patient Information</h4>
                                </div>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Name:</span>
                                        <button
                                            @click="$emit('view-patient', currentAppointment.patient)"
                                            class="text-teal-600 hover:text-teal-800 font-medium transition-colors"
                                        >
                                            {{ currentAppointment.patient?.user?.name || currentAppointment.patient_name || 'N/A' }}
                                        </button>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Email:</span>
                                        <span class="text-gray-900">{{ currentAppointment.patient?.user?.email || 'N/A' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Phone:</span>
                                        <span class="text-gray-900">{{ currentAppointment.patient?.user?.phone_number || currentAppointment.patient?.phone_number || 'N/A' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Patient ID:</span>
                                        <span class="text-gray-900">{{ currentAppointment.patient?.patient_unique_id || `P${currentAppointment.patient?.id}` || 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Provider Information -->
                            <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 border border-emerald-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <Icon name="user-check" class="text-emerald-600 w-4 h-4 mr-2" />
                                    <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Provider Information</h4>
                                </div>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Name:</span>
                                        <span class="text-gray-900">{{ currentAppointment.provider?.user?.name || currentAppointment.provider_name || 'N/A' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Specialization:</span>
                                        <span class="text-gray-900">{{ currentAppointment.provider?.specialization || 'General Practice' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="font-medium text-gray-700">Clinic:</span>
                                        <span class="text-gray-900">{{ currentAppointment.clinic?.name || 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                            <div class="flex items-center mb-4">
                                <Icon name="credit-card" class="text-amber-600 w-4 h-4 mr-2" />
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Payment Information</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Amount:</span>
                                    <span class="text-gray-900 font-semibold">£{{ formatCurrency(currentAppointment.amount || currentAppointment.service?.price || 0) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Payment Status:</span>
                                    <span :class="getPaymentBadgeClass(currentAppointment.payment_status)" class="inline-flex px-2 py-0.5 text-xs font-medium rounded-md">
                                        {{ formatPaymentStatus(currentAppointment.payment_status) }}
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Method:</span>
                                    <span class="text-gray-900">{{ currentAppointment.payment_method || 'N/A' }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Notes Section -->
                        <div v-if="currentAppointment.notes || currentAppointment.cancellation_reason" class="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-lg p-5 hover:shadow-sm transition-shadow duration-200">
                            <div class="flex items-center mb-4">
                                <Icon name="sticky-note" class="text-slate-600 w-4 h-4 mr-2" />
                                <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wide">Additional Information</h4>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div v-if="currentAppointment.status === 'rescheduled' && currentAppointment.notes">
                                    <span class="font-medium text-purple-700 block mb-1">Reschedule Reason:</span>
                                    <p class="text-gray-900 bg-purple-50 p-3 rounded-md border border-purple-200">{{ currentAppointment.notes }}</p>
                                </div>
                                <div v-else-if="currentAppointment.notes">
                                    <span class="font-medium text-gray-700 block mb-1">Notes:</span>
                                    <p class="text-gray-900 bg-white p-3 rounded-md border">{{ currentAppointment.notes }}</p>
                                </div>
                                <div v-if="currentAppointment.cancellation_reason">
                                    <span class="font-medium text-red-700 block mb-1">Cancellation Reason:</span>
                                    <p class="text-red-800 bg-red-50 p-3 rounded-md border border-red-200">{{ currentAppointment.cancellation_reason }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div v-else class="text-center py-8">
                        <Icon name="calendar-x" class="text-gray-400 w-16 h-16 mx-auto mb-4" />
                        <p class="text-gray-500">No appointment information available</p>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-white px-6 py-4 border-t border-gray-200 flex justify-between items-center rounded-b-lg">
                    <div class="text-xs text-gray-500">
                        Last updated: {{ formatDateTime(appointment?.updated_at) }}
                    </div>
                    <div class="flex space-x-3">
                        <button @click="$emit('close')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Close
                        </button>
                        <button v-if="showEditButton && appointment" @click="$emit('edit', appointment)" class="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2">
                            Edit Appointment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import axios from 'axios'
import Icon from '@/components/Icon.vue'

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false
    },
    appointment: {
        type: Object,
        default: null
    },
    appointmentId: {
        type: [Number, String],
        default: null
    },
    showEditButton: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['close', 'edit', 'view-patient'])

// Internal state
const loading = ref(false)
const error = ref(null)
const fetchedAppointment = ref(null)

// Computed appointment - use passed appointment or fetched one
const currentAppointment = computed(() => {
    return props.appointment || fetchedAppointment.value
})

// Fetch appointment details when appointmentId is provided and modal opens
const fetchAppointmentDetails = async () => {
    if (!props.appointmentId) return

    loading.value = true
    error.value = null

    try {
        const response = await axios.get(`/appointments/${props.appointmentId}`)
        fetchedAppointment.value = response.data
    } catch (err) {
        console.error('Error fetching appointment details:', err)
        error.value = 'Failed to load appointment details'
    } finally {
        loading.value = false
    }
}

// Watch for modal opening and appointmentId changes
watch([() => props.isOpen, () => props.appointmentId], ([isOpen, appointmentId]) => {
    if (isOpen && appointmentId && !props.appointment) {
        fetchAppointmentDetails()
    }
}, { immediate: true })

// Clear fetched data when modal closes
watch(() => props.isOpen, (isOpen) => {
    if (!isOpen) {
        fetchedAppointment.value = null
        error.value = null
    }
})

// Helper functions for formatting and styling
const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}

const formatTime = (timeString) => {
    if (!timeString) return 'N/A'
    
    // Handle time_slot object
    if (typeof timeString === 'object' && timeString.start_time) {
        return timeString.start_time
    }
    
    return new Date(timeString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    })
}

const formatCurrency = (amount) => {
    if (!amount) return '0.00'
    return parseFloat(amount).toFixed(2)
}

const formatStatus = (status) => {
    if (!status) return 'Unknown'
    return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const formatPaymentStatus = (status) => {
    if (!status) return 'Unknown'
    return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

// Status badge classes
const getStatusBadgeClass = (status) => {
    const classes = {
        'rescheduled': 'bg-purple-100 text-purple-800 border border-purple-200',
        'scheduled': 'bg-blue-100 text-blue-800 border border-blue-200',
        'in_progress': 'bg-yellow-100 text-yellow-800 border border-yellow-200',
        'completed': 'bg-green-100 text-green-800 border border-green-200',
        'cancelled': 'bg-red-100 text-red-800 border border-red-200',
        'no_show': 'bg-gray-100 text-gray-800 border border-gray-200'
    }
    return classes[status] || 'bg-gray-100 text-gray-800 border border-gray-200'
}

const getTypeBadgeClass = (isTelemedicine) => {
    return isTelemedicine 
        ? 'bg-purple-100 text-purple-800 border border-purple-200'
        : 'bg-blue-100 text-blue-800 border border-blue-200'
}

const getPaymentBadgeClass = (status) => {
    const classes = {
        'paid': 'bg-emerald-100 text-emerald-800 border border-emerald-200',
        'pending': 'bg-orange-100 text-orange-800 border border-orange-200',
        'failed': 'bg-red-100 text-red-800 border border-red-200',
        'refunded': 'bg-gray-100 text-gray-800 border border-gray-200'
    }
    return classes[status] || 'bg-gray-100 text-gray-800 border border-gray-200'
}

// Check-in status helpers
const getCheckInStatus = (appointment) => {
    if (appointment?.checked_out_at) {
        return 'Checked Out'
    } else if (appointment?.checked_in_at) {
        return 'Ready for Consultation'
    } else {
        return 'Pending Check-in'
    }
}

const getCheckInIcon = (appointment) => {
    if (appointment?.checked_out_at) {
        return 'log-out'
    } else if (appointment?.checked_in_at) {
        return 'check-circle'
    } else {
        return 'clock'
    }
}

const getCheckInBadgeClass = (appointment) => {
    if (appointment?.checked_out_at) {
        return 'bg-gray-100 text-gray-700 border border-gray-200'
    } else if (appointment?.checked_in_at) {
        return 'bg-emerald-100 text-emerald-800 border border-emerald-200'
    } else {
        return 'bg-orange-100 text-orange-700 border border-orange-200'
    }
}
</script>
