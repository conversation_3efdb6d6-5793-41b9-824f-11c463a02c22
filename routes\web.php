<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Core Controllers
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ManagementController;
use App\Http\Controllers\AnalyticsController;

// Authentication Controllers
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\PermissionController;
use App\Http\Controllers\ImpersonationController;

// Healthcare Controllers
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\AppointmentBookingController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\ProviderDashboardController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\ProviderRegistrationController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\PatientPreferencesController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ClinicController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\VideoConsultationController;

// Clinical Controllers
use App\Http\Controllers\ConsultationController;
use App\Http\Controllers\ConsultationDocumentController;
use App\Http\Controllers\ConsultationNoteController;
use App\Http\Controllers\ConsultationPrescriptionController;
use App\Http\Controllers\ConsultationTabController;
use App\Http\Controllers\DiagnosisController;
use App\Http\Controllers\MedicationController;
use App\Http\Controllers\PrescriptionController;
use App\Http\Controllers\PrescriptionRefillController;
use App\Http\Controllers\TreatmentPlanController;
use App\Http\Controllers\DoctorSettingsController;
use App\Http\Controllers\MedicalLetterController;
use App\Http\Controllers\TdlTestCatalogController;
use App\Http\Controllers\TdlLabRequestController;
use App\Http\Controllers\TdlLabResultController;
use App\Http\Controllers\TdlSettingsController;
use App\Http\Controllers\DrugSearchController;

use App\Http\Controllers\Icd10Controller;

// Billing Controllers
use App\Http\Controllers\TaxController;
use App\Http\Controllers\BillController;
use App\Http\Controllers\BillItemController;
use App\Http\Controllers\ConsultationServiceController;
use App\Http\Controllers\ConsultationSharingController;
use App\Http\Controllers\LetterheadController;

// Communication Controllers
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AIChatController;
use App\Http\Controllers\WellnessController;
use App\Http\Controllers\EmailTemplateController;
use App\Http\Controllers\NotificationTemplateController;
use App\Http\Controllers\NotificationManagementController;

// Social & Content Controllers
use App\Http\Controllers\SocialFeedController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\StoryController;
use App\Http\Controllers\AIContentController;
use App\Http\Controllers\FileController;

// E-commerce Controllers
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductReviewController;
use App\Http\Controllers\ShoppingCartController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DigitalDownloadController;

// Integration Controllers
use App\Http\Controllers\InstagramController;
use App\Http\Controllers\InstagramAuthController;
use App\Http\Controllers\InstagramWebhookController;
use App\Http\Controllers\StripeConfigController;

// Admin Controllers
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\BotManagementController;
use App\Http\Controllers\Admin\ClinicianManagementController;
use App\Http\Controllers\Admin\ClubManagementController;
use App\Http\Controllers\Admin\OrderManagementController;
use App\Http\Controllers\Admin\CountryController;
use App\Http\Controllers\Admin\ProductCategoryController;
use App\Http\Controllers\Admin\ProductManagementController;
use App\Http\Controllers\Admin\SocialMediaManagerController;
use App\Http\Controllers\Admin\WaitlistController as AdminWaitlistController;

// Provider Controllers
use App\Http\Controllers\Provider\OrderFulfillmentController;

// System Controllers
use App\Http\Controllers\SystemVerificationController;
use App\Http\Controllers\UserCreditController;
use App\Http\Controllers\WaitlistController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\MembershipController;
use App\Http\Controllers\CheckoutController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

/*
|--------------------------------------------------------------------------
| System Routes
|--------------------------------------------------------------------------
*/



// CSRF token endpoint with error suppression
Route::get('/csrf-token', function () {
    // Suppress any notices/warnings for this endpoint
    $previousErrorReporting = error_reporting(E_ERROR | E_WARNING | E_PARSE);

    try {
        $token = csrf_token();
        return response()->json(['csrf_token' => $token]);
    } finally {
        // Restore previous error reporting level
        error_reporting($previousErrorReporting);
    }
});

// Stripe Configuration Routes (no auth required for public booking)
Route::get('stripe/config', [StripeConfigController::class, 'getConfig']);
Route::get('stripe/check-clinic', [StripeConfigController::class, 'checkClinicConfig']);

/*
|--------------------------------------------------------------------------
| Public Routes (No Authentication Required)
|--------------------------------------------------------------------------
*/

// Home page
Route::get('/', [WebController::class, 'index'])->name('home');

// Legal pages
Route::get('policies', [WebController::class, 'allPolicies'])->name('all-policies');
Route::get('terms-of-service', [WebController::class, 'termsOfService'])->name('terms-of-service');
Route::get('privacy-policy', [WebController::class, 'privacyPolicyNew'])->name('privacy-policy');
Route::get('terms-of-use-indian-users-only', [WebController::class, 'termsOfUseIndian'])->name('terms-of-use-indian');
Route::get('tandcofsale-us-uk', [WebController::class, 'termsAndConditionsOfSale'])->name('terms-conditions-sale');

// Legacy routes - redirect to centralized policies page
Route::get('terms-and-conditions', function() { return redirect('/policies'); });
Route::get('privacy', function() { return redirect('/policies'); });
Route::get('legal', function() { return redirect('/policies'); });

// Public membership routes - no authentication required for viewing
Route::prefix('membership')->group(function () {
    Route::get('/', [MembershipController::class, 'index'])->name('membership.index');
    Route::get('plan/{slug}', [MembershipController::class, 'showPlan'])->name('membership.plan');
    Route::get('checkout/{slug}', [MembershipController::class, 'checkout'])->name('membership.checkout');
    Route::post('checkout/{slug}/process', [MembershipController::class, 'processPayment'])->name('membership.process-payment');
    Route::get('thank-you/{slug}', [MembershipController::class, 'thankYou'])->name('membership.thank-you');
});

// Public content routes
Route::get('post/{postId}', [WebController::class, 'publicSharedPost'])
    ->name('public.shared-post')
    ->where('postId', '[0-9]+');
Route::get('discover-public', [WebController::class, 'publicDiscover'])
    ->name('public.discover');

// Public shared prescription view
Route::get('shared/prescription/{token}', [PrescriptionController::class, 'viewSharedPrescription'])
    ->name('prescription.shared');

// Public appointment booking
Route::prefix('book-appointment')->group(function () {
    Route::get('{clinicSlug}', [AppointmentBookingController::class, 'index'])
        ->name('appointment.booking');
    Route::post('{clinicSlug}/check-email', [AppointmentBookingController::class, 'checkEmail'])
        ->name('appointment.booking.check-email');
    Route::post('{clinicSlug}/verify-login', [AppointmentBookingController::class, 'verifyLogin'])
        ->name('appointment.booking.verify-login');
    Route::post('{clinicSlug}/forgot-password', [AppointmentBookingController::class, 'forgotPassword'])
        ->name('appointment.booking.forgot-password');
    Route::get('{clinicSlug}/categories', [AppointmentBookingController::class, 'getCategories'])
        ->name('appointment.booking.categories');
    Route::get('{clinicSlug}/categories/{categoryId}/services', [AppointmentBookingController::class, 'getServices'])
        ->name('appointment.booking.services');
    Route::get('{clinicSlug}/services/{serviceId}/dates', [AppointmentBookingController::class, 'getAvailableDates'])
        ->name('appointment.booking.dates');
    Route::get('{clinicSlug}/services/{serviceId}/slots', [AppointmentBookingController::class, 'getTimeSlots'])
        ->name('appointment.booking.slots');
    Route::post('{clinicSlug}/create-payment-intent', [AppointmentBookingController::class, 'createPaymentIntent'])
        ->name('appointment.booking.payment-intent');
    Route::post('{clinicSlug}/complete-booking', [AppointmentBookingController::class, 'completeBooking'])
        ->name('appointment.booking.complete');
    Route::post('{clinicSlug}/book', [AppointmentBookingController::class, 'bookAppointment'])
        ->name('appointment.booking.book');
    Route::post('{clinicSlug}/saved-cards', [AppointmentBookingController::class, 'getSavedCards'])
        ->name('appointment.booking.saved-cards');
    Route::delete('{clinicSlug}/saved-cards/{cardId}', [AppointmentBookingController::class, 'deleteSavedCard'])
        ->name('appointment.booking.delete-card');
});

// Provider registration
Route::get('providers/register', [ProviderRegistrationController::class, 'showRegistrationPage'])
    ->name('providers.register');

// Founder Club registration
Route::get('join-founders', [RegisteredUserController::class, 'showFounderSignup'])
    ->name('founder.signup');
Route::post('join-founders', [RegisteredUserController::class, 'storeFounderSignup'])
    ->name('founder.signup.store');

// Anonymous chat
Route::prefix('anonymous/chat')->group(function () {
    Route::post('start', [AIChatController::class, 'startAnonymousConversation']);
    Route::post('message', [AIChatController::class, 'sendAnonymousMessage']);
});

/*
|--------------------------------------------------------------------------
| External Integrations (No Authentication Required)
|--------------------------------------------------------------------------
*/

// Instagram webhooks
Route::get('webhooks/instagram', [InstagramWebhookController::class, 'verify']);
Route::post('webhooks/instagram', [InstagramWebhookController::class, 'handle']);
Route::get('auth/instagram/callback', [InstagramController::class, 'handleCallback']);

// Digital downloads
Route::get('download/{token}', [DigitalDownloadController::class, 'download'])
    ->name('digital-download');

// Public mobile recording page (no authentication required)
Route::get('/mobile-record/{sessionId}', function ($sessionId) {
    return view('mobile-record', ['sessionId' => $sessionId]);
})->name('mobile-record');

// Public mobile recording upload (no authentication required)
Route::post('/consultations/mobile-recording/upload', [ConsultationController::class, 'uploadMobileRecording']);

// Public mobile upload page (no authentication required)
Route::get('/mobile-upload/{sessionId}', function ($sessionId) {
    return view('mobile-upload', ['sessionId' => $sessionId]);
})->name('mobile-upload');

// Public mobile file upload (no authentication required)
Route::post('/consultations/mobile-upload', [ConsultationDocumentController::class, 'processMobileUpload']);

// Public waitlist status (no authentication required)
Route::get('waitlist/status', [WaitlistController::class, 'getWaitlistStatus']);

/*
|--------------------------------------------------------------------------
| Authenticated Routes
|--------------------------------------------------------------------------
*/

// Main dashboard
Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Core authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard data
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])
        ->name('dashboard.data');
    Route::get('provider/dashboard-data', [ProviderDashboardController::class, 'getDashboardData'])
        ->name('provider.dashboard.data');
    Route::get('consultation-dashboard-data', [ConsultationController::class, 'getDashboardData'])
        ->name('consultation.dashboard.data');


    Route::get('management/dashboard/kpi', [AnalyticsController::class, 'getKpiMetrics'])
        ->name('management.dashboard.kpi');

    // Provider availability
    Route::get('provider/get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability'])
        ->name('provider.availability.get');
    Route::get('provider/get-absences', [ProviderAvailabilityController::class, 'getAbsences'])
        ->name('provider.absences.get');

    // Healthcare routes
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])
        ->name('providers.show');

    // Appointment routes - authenticated users get management interface
    Route::get('appointments', [ManagementController::class, 'appointments'])
        ->middleware(['auth', 'permission:view appointments'])
        ->name('appointments');
    Route::get('appointments/{id}', [ManagementController::class, 'viewAppointment'])
        ->middleware(['auth', 'permission:view appointments'])
        ->name('appointments.view');
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])
        ->name('appointments.create');

    Route::get('appointments/{appointment}/payment', [PaymentController::class, 'showPaymentPage'])
        ->name('appointments.payment');

    // Video consultation routes
    Route::prefix('video')->group(function () {
        Route::post('initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])
            ->name('video.initialize');
        Route::get('session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])
            ->name('video.session');
        Route::post('join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])
            ->name('video.join');
        Route::post('leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])
            ->name('video.leave');
        Route::get('status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])
            ->name('video.status');
        Route::post('participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])
            ->name('video.participant-disconnected');
        Route::post('end/{appointmentId}', [VideoConsultationController::class, 'endSession'])
            ->name('video.end');
    });

    // Chat routes
    Route::get('chat', [ChatController::class, 'webIndex'])
        ->name('chat');

    // Communication & Chat routes
    Route::prefix('chat')->group(function () {
        Route::post('start', [AIChatController::class, 'startConversation'])
            ->name('chat.start');
        Route::post('message', [AIChatController::class, 'sendMessage'])
            ->name('chat.message');
        Route::get('history', [AIChatController::class, 'getHistory'])
            ->name('chat.history');
        Route::get('conversation/{conversationId}', [AIChatController::class, 'getConversation'])
            ->name('chat.conversation');
        Route::post('transfer-anonymous', [ChatController::class, 'transferAnonymousConversation'])
            ->name('chat.transfer-anonymous');
        Route::post('update-titles', [ChatController::class, 'updateTitles'])
            ->name('chat.update-titles');
    });

    // Wellness Chat routes
    Route::prefix('wellness')->group(function () {
        Route::post('start', [WellnessController::class, 'startConversation'])
            ->name('wellness.start');
        Route::post('message', [WellnessController::class, 'sendMessage'])
            ->name('wellness.message');
        Route::get('conversations', [WellnessController::class, 'getConversations'])
            ->name('wellness.conversations');
        Route::get('conversation/{id}', [WellnessController::class, 'getConversation'])
            ->name('wellness.conversation');
        Route::get('status', [WellnessController::class, 'getStatus'])
            ->name('wellness.status');
    });

    /*
    |--------------------------------------------------------------------------
    | Social Feed & Content Routes
    |--------------------------------------------------------------------------
    */

    // Discover and social content
    Route::get('discover', [WebController::class, 'discover'])->name('discover');
    Route::get('discover/post/{postId}', [WebController::class, 'sharedPost'])->name('discover.shared-post');
    Route::get('saved-posts', [SocialFeedController::class, 'getSavedPosts']);

    // Public feed routes (read-only)
    Route::prefix('feed')->group(function () {
        Route::get('/', [SocialFeedController::class, 'index'])->middleware('performance:feed_loading');
        Route::get('search', [SocialFeedController::class, 'search'])->middleware('performance:feed_loading');
        Route::get('post/{postId}', [SocialFeedController::class, 'getPost']);
        Route::get('topics', [SocialFeedController::class, 'topics']);
        Route::get('{contentId}', [SocialFeedController::class, 'getPost']); // For sharing
        Route::get('{contentId}/comments', [SocialFeedController::class, 'getComments']);
        Route::get('{contentId}/replies', [CommentController::class, 'getReplies']);

        // Authenticated feed actions
        Route::post('create', [SocialFeedController::class, 'create']);
        Route::post('like/{contentId}', [SocialFeedController::class, 'likeContent']);
        Route::post('save/{contentId}', [SocialFeedController::class, 'savePost']);
        Route::delete('save/{contentId}', [SocialFeedController::class, 'unsavePost']);
        Route::post('{contentId}/share', [SocialFeedController::class, 'shareContent']);
        Route::post('{contentId}/report', [SocialFeedController::class, 'reportContent']);
        Route::post('{contentId}/comments', [SocialFeedController::class, 'addComment']);
        Route::delete('{contentId}', [SocialFeedController::class, 'deleteContent']);

        Route::prefix('{contentId}/comments')->group(function () {
            Route::delete('{commentId}', [CommentController::class, 'destroy']);
            Route::post('{commentId}/react', [CommentController::class, 'react']);
            Route::post('{commentId}/reply', [CommentController::class, 'storeReply']);
        });
    });

    /*
    |--------------------------------------------------------------------------
    | E-commerce & Shop Routes
    |--------------------------------------------------------------------------
    */

    Route::get('shop', [WebController::class, 'shop'])->name('shop');
    Route::get('chat-history', [WebController::class, 'chatHistory'])->name('chat-history');
    Route::get('credit-history', [WebController::class, 'creditHistory'])->name('credit-history');

    // Checkout routes
    Route::prefix('checkout')->group(function () {
        Route::get('appointment/{serviceId?}', [CheckoutController::class, 'appointmentCheckout'])->name('checkout.appointment');
        Route::get('product', [CheckoutController::class, 'productCheckout'])->name('checkout.product');
        Route::get('subscription/{planId}', [CheckoutController::class, 'subscriptionCheckout'])->name('checkout.subscription');
        Route::post('process/appointment', [CheckoutController::class, 'processAppointment'])->name('checkout.process.appointment');
        Route::post('process/product', [CheckoutController::class, 'processProduct'])->name('checkout.process.product');
        Route::post('process/subscription', [CheckoutController::class, 'processSubscription'])->name('checkout.process.subscription');
    });

    /*
    |--------------------------------------------------------------------------
    | AI Content & Stories Routes
    |--------------------------------------------------------------------------
    */

    // AI Content Generation
    Route::post('ai/generate-content', [AIContentController::class, 'generateContent']);

    // Stories management
    Route::prefix('stories')->group(function () {
        Route::get('/', [StoryController::class, 'index']);
        Route::post('/', [StoryController::class, 'store']);
        Route::get('user/{userId}', [StoryController::class, 'getUserStories']);
        Route::post('{storyId}/view', [StoryController::class, 'markAsViewed']);
        Route::delete('{storyId}', [StoryController::class, 'destroy']);
        Route::get('{storyId}/viewers', [StoryController::class, 'getViewers']);
    });

    /*
    |--------------------------------------------------------------------------
    | User Profile & File Management Routes
    |--------------------------------------------------------------------------
    */

    // User profile management
    Route::prefix('user')->group(function () {
        Route::get('profile-stats', [UserController::class, 'getProfileStats']);
        Route::get('{userId}/profile-stats', [UserController::class, 'getUserProfileStats']);
        Route::post('profile-image', [UserController::class, 'updateProfileImage']);
        Route::post('bio', [UserController::class, 'updateBio']);
        Route::post('{userId}/follow', [UserController::class, 'followUser']);
        Route::post('{userId}/unfollow', [UserController::class, 'unfollowUser']);
        Route::get('{userId}/followers', [UserController::class, 'getFollowers']);
        Route::get('{userId}/following', [UserController::class, 'getFollowing']);
    });

    // File Management
    Route::get('files', function () {
        return Inertia::render('FileManager');
    })->name('files');

    Route::prefix('files')->group(function () {
        Route::get('/', [FileController::class, 'index']);
        Route::post('/', [FileController::class, 'store']);
        Route::get('/categories', [FileController::class, 'getCategories']);
        Route::get('/stats', [FileController::class, 'getStats']);
        Route::post('/bulk-delete', [FileController::class, 'bulkDelete']);
        Route::get('/{id}', [FileController::class, 'show']);
        Route::put('/{id}', [FileController::class, 'update']);
        Route::delete('/{id}', [FileController::class, 'destroy']);
        Route::get('/{id}/download', [FileController::class, 'download']);
        Route::get('/{id}/secure-url', [FileController::class, 'getSecureUrl']);
        Route::post('/{id}/optimize', [FileController::class, 'optimizeImage']);
        Route::post('/{id}/thumbnails', [FileController::class, 'generateThumbnails']);
        Route::get('/{id}/thumbnail', [FileController::class, 'getThumbnail']);
    });
});

/*
|--------------------------------------------------------------------------
| Role-Based Routes
|--------------------------------------------------------------------------
*/

// Admin routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->group(function () {
    // Social Media Management
    Route::prefix('social-media')->group(function () {
        Route::get('/', [SocialMediaManagerController::class, 'index']);
        Route::get('posts', [SocialMediaManagerController::class, 'getPosts']);
        Route::get('stories', [SocialMediaManagerController::class, 'getStories']);
        Route::patch('posts/{postId}/status', [SocialMediaManagerController::class, 'updatePostStatus']);
        Route::delete('posts/{postId}', [SocialMediaManagerController::class, 'deletePost']);
        Route::delete('stories/{storyId}', [SocialMediaManagerController::class, 'deleteStory']);
    });
});

// Provider routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    // Dashboard & Core Provider Routes
    Route::get('dashboard', [ProviderDashboardController::class, 'index'])
        ->name('provider.dashboard');
    Route::get('appointments', [AppointmentController::class, 'webIndex'])
        ->name('provider.appointments');
    Route::get('availability', [ProviderController::class, 'availability'])
        ->name('provider.availability');
    Route::get('services', [ProviderController::class, 'services'])
        ->name('provider.services');
    Route::get('schedule', [ProviderController::class, 'schedule'])
        ->name('provider.schedule');
    Route::get('patients', [ProviderController::class, 'patients'])
        ->name('provider.patients');
    Route::get('earnings', [ProviderController::class, 'earnings'])
        ->name('provider.earnings');
    Route::get('profile', function () {
        return redirect('/settings/profile');
    })->name('provider.profile');

    // Product Management
    Route::prefix('products')->group(function () {
        // Bulk import routes
        Route::get('import-template', [ProductManagementController::class, 'downloadImportTemplate']);
        Route::get('import-instructions', [ProductManagementController::class, 'downloadImportInstructions']);
        Route::post('validate-import', [ProductManagementController::class, 'validateImport']);
        Route::post('bulk-import', [ProductManagementController::class, 'bulkImport']);

        // CRUD routes
        Route::get('/', [ProductManagementController::class, 'index'])->name('provider.products');
        Route::get('create', [ProductManagementController::class, 'create'])->name('provider.products.create');
        Route::post('/', [ProductManagementController::class, 'store'])->name('provider.products.store');
        Route::get('{id}', [ProductManagementController::class, 'show'])->name('provider.products.show');
        Route::get('{id}/edit', [ProductManagementController::class, 'edit'])->name('provider.products.edit');
        Route::put('{id}', [ProductManagementController::class, 'update'])->name('provider.products.update');
        Route::delete('{id}', [ProductManagementController::class, 'destroy'])->name('provider.products.destroy');
        Route::patch('{id}/toggle-status', [ProductManagementController::class, 'toggleStatus'])->name('provider.products.toggle-status');
    });

    // Order Fulfillment
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderFulfillmentController::class, 'index'])->name('provider.orders');
        Route::get('{id}', [OrderFulfillmentController::class, 'show'])->name('provider.orders.show');
        Route::post('{id}/dispatch', [OrderFulfillmentController::class, 'dispatch'])->name('provider.orders.dispatch');
        Route::patch('{id}/status', [OrderFulfillmentController::class, 'updateStatus'])->name('provider.orders.update-status');
    });

    Route::get('shipping-companies', [OrderFulfillmentController::class, 'getShippingCompanies'])
        ->name('provider.shipping-companies');
});

// Patient-specific routes
Route::middleware(['auth', 'verified', 'role:patient'])->prefix('patient')->group(function () {
    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('patient.appointments');
});

// Admin/Manager routes that don't require specific permissions (fallback for role-based access)
Route::middleware(['auth', 'verified'])->group(function () {
    // Other authenticated routes can go here
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', [ManagementController::class, 'users'])->name('users')->middleware('permission:view users');

    // Patient management - requires 'view patients' permission
    Route::get('patients', [ManagementController::class, 'patients'])->name('patients')->middleware('permission:view patients');
    Route::get('patients/{id}/edit', [ManagementController::class, 'editPatient'])->name('patients.edit')->middleware('permission:edit patients');
    Route::get('patients/{id}/consultations', [ManagementController::class, 'patientConsultations'])->name('patients.consultations')->middleware('permission:view patients');
    Route::get('patients/{id}/appointments', function ($id) {
        return Inertia::render('Patients/Appointments', ['patientId' => $id]);
    })->name('patients.appointments')->middleware('permission:view patients');



    // Payment management - requires 'view payments' permission
    Route::get('payments', [ManagementController::class, 'payments'])->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', [ManagementController::class, 'chats'])->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'view permissions' permission
    Route::get('permissions', [ManagementController::class, 'permissions'])->name('permissions')->middleware('permission:view permissions');

    // Email template management - requires 'view email templates' permission
    Route::get('email-templates', [ManagementController::class, 'emailTemplates'])->name('email-templates')->middleware('permission:view email templates');



    // Notification management - requires 'view notifications' permission
    Route::get('notifications', [ManagementController::class, 'notifications'])->name('notifications')->middleware('permission:view notifications');

    // Waitlist management - requires 'view users' permission
    Route::get('waitlist', function () {
        return Inertia::render('Waitlist');
    })->name('waitlist')->middleware('permission:view users');

    // Service management - requires 'view services' permission
    Route::get('services', [ManagementController::class, 'services'])->name('services')->middleware('permission:view services');

    // Pending approvals - requires admin role
    Route::get('pending-approvals', function () {
        return Inertia::render('PendingApprovals');
    })->name('pending-approvals')->middleware('role:admin');

    // System verification - requires admin permissions
    Route::get('system-verification', [ManagementController::class, 'systemVerification'])->name('system-verification')->middleware('permission:view users');

    // Referral management - requires 'view referrals' permission
    Route::get('referrals', [ManagementController::class, 'referrals'])->name('referrals')->middleware('permission:view referrals');

    // Credit management - requires 'view credits' permission
    Route::get('credits', [ManagementController::class, 'credits'])->name('credits')->middleware('permission:view credits');

    // Club management - requires 'view clubs' permission
    Route::get('clubs', [ManagementController::class, 'clubs'])->name('clubs')->middleware('permission:view clubs');

    // Ecommerce management routes
    Route::prefix('admin')->group(function () {
        // Bulk import routes (must be before products/{id} routes)
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate'])->middleware('permission:create products');
        Route::get('products/import-instructions', [ProductManagementController::class, 'downloadImportInstructions'])->middleware('permission:create products');
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport'])->middleware('permission:create products');
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport'])->middleware('permission:create products');

        // Product management - requires product permissions
        Route::get('products', [ProductManagementController::class, 'index'])->name('admin.products')->middleware('permission:view products');
        Route::get('products/create', [ProductManagementController::class, 'create'])->name('admin.products.create')->middleware('permission:create products');
        Route::post('products', [ProductManagementController::class, 'store'])->name('admin.products.store')->middleware('permission:create products');
        Route::get('products/{id}', [ProductManagementController::class, 'show'])->name('admin.products.show')->middleware('permission:view products');
        Route::get('products/{id}/edit', [ProductManagementController::class, 'edit'])->name('admin.products.edit')->middleware('permission:edit products');
        Route::put('products/{id}', [ProductManagementController::class, 'update'])->name('admin.products.update')->middleware('permission:edit products');
        Route::delete('products/{id}', [ProductManagementController::class, 'destroy'])->name('admin.products.destroy')->middleware('permission:delete products');
        Route::patch('products/{id}/toggle-status', [ProductManagementController::class, 'toggleStatus'])->name('admin.products.toggle-status')->middleware('permission:edit products');

        // Product category management - requires product permissions
        Route::get('categories', [ProductCategoryController::class, 'index'])->name('admin.categories')->middleware('permission:view products');
        Route::get('categories/create', [ProductCategoryController::class, 'create'])->name('admin.categories.create')->middleware('permission:create products');
        Route::post('categories', [ProductCategoryController::class, 'store'])->name('admin.categories.store')->middleware('permission:create products');
        Route::get('categories/{id}', [ProductCategoryController::class, 'show'])->name('admin.categories.show')->middleware('permission:view products');
        Route::get('categories/{id}/edit', [ProductCategoryController::class, 'edit'])->name('admin.categories.edit')->middleware('permission:edit products');
        Route::put('categories/{id}', [ProductCategoryController::class, 'update'])->name('admin.categories.update')->middleware('permission:edit products');
        Route::delete('categories/{id}', [ProductCategoryController::class, 'destroy'])->name('admin.categories.destroy')->middleware('permission:delete products');
        Route::patch('categories/{id}/toggle-status', [ProductCategoryController::class, 'toggleStatus'])->name('admin.categories.toggle-status')->middleware('permission:edit products');

        // Order management - requires 'manage orders' permission or admin role
        Route::get('orders', [OrderManagementController::class, 'index'])->name('admin.orders');
        Route::get('orders/{id}', [OrderManagementController::class, 'show'])->name('admin.orders.show');
        Route::patch('orders/{id}/status', [OrderManagementController::class, 'updateStatus'])->name('admin.orders.update-status');
        Route::patch('orders/{id}/payment-status', [OrderManagementController::class, 'updatePaymentStatus'])->name('admin.orders.update-payment-status');
        Route::post('orders/{id}/notes', [OrderManagementController::class, 'addNote'])->name('admin.orders.add-note');
        Route::get('orders/stats', [OrderManagementController::class, 'getOrderStats'])->name('admin.orders.stats');
        Route::get('orders/export', [OrderManagementController::class, 'exportOrders'])->name('admin.orders.export');

        /*
        |--------------------------------------------------------------------------
        | Country Management Routes (Admin Only)
        |--------------------------------------------------------------------------
        */

        // Country CRUD operations
        Route::resource('countries', CountryController::class)->names([
            'index' => 'admin.countries.index',
            'create' => 'admin.countries.create',
            'store' => 'admin.countries.store',
            'show' => 'admin.countries.show',
            'edit' => 'admin.countries.edit',
            'update' => 'admin.countries.update',
            'destroy' => 'admin.countries.destroy',
        ])->middleware('role:admin');

        // Country status management
        Route::patch('countries/{country}/toggle-status', [CountryController::class, 'toggleStatus'])
            ->name('admin.countries.toggle-status')
            ->middleware('role:admin');
        Route::patch('countries/{country}/toggle-support', [CountryController::class, 'toggleSupport'])
            ->name('admin.countries.toggle-support')
            ->middleware('role:admin');
    });
});

// API routes for web application (session-based authentication)
Route::middleware(['auth', 'verified'])->group(function () {
    // Services management routes (following availability pattern)
    Route::get('services-list', [ServiceController::class, 'getServices']);
    Route::get('services-list-dropdown', [ServiceController::class, 'getServicesListDropdown']);
    Route::post('save-service', [ServiceController::class, 'store']);
    Route::put('save-service/{id}', [ServiceController::class, 'update']);
    Route::delete('delete-service/{id}', [ServiceController::class, 'destroy']);
    Route::get('get-service-categories', [ServiceController::class, 'getCategories']);

    // Providers API routes (clean URLs for Vue rendering)
    Route::get('providers', [ManagementController::class, 'providers'])->name('providers')->middleware('permission:view providers');

    // Providers management routes (following availability pattern)
    Route::get('providers-list', [ProviderController::class, 'getProvidersList'])->middleware('permission:view providers');
    Route::get('providers-list-dropdown', [ProviderController::class, 'getProvidersListDropdown']);
    Route::get('providers/specializations', [ProviderController::class, 'getSpecializations']);

    // This route must come after more specific routes like providers/specializations
    Route::get('providers/{id}', [ProviderController::class, 'show']);
    Route::get('get-providers', [ProviderController::class, 'index']);
    Route::get('get-providers/{id}', [ProviderController::class, 'show']);
    Route::post('save-provider', [ProviderController::class, 'store']);
    Route::post('save-provider-with-user', [ProviderController::class, 'storeWithUser']);
    Route::put('update-provider/{id}', [ProviderController::class, 'update']);
    Route::put('update-provider-with-user/{id}', [ProviderController::class, 'updateWithUser']);
    Route::delete('delete-provider/{id}', [ProviderController::class, 'destroy']);

    // Users management routes
    Route::get('users-list', [UserController::class, 'index']);
    Route::get('users-without-provider', [UserController::class, 'getUsersWithoutProvider']);
    Route::put('users/{id}', [UserController::class, 'update'])->middleware('permission:edit users');
    Route::patch('users/{id}/toggle-status', [UserController::class, 'toggleStatus']);
    Route::post('users/{id}/assign-role', [UserController::class, 'assignRole']);
    Route::post('users', [UserController::class, 'store']);

    // User clinic info route
    Route::get('user-clinic-info', [UserController::class, 'getUserClinicInfo']);

    // Clinician access management
    Route::post('users/{user}/toggle-clinician', [ClinicianManagementController::class, 'toggleClinicianAccess']);

    /*
    |--------------------------------------------------------------------------
    | Clinical Functionality Routes (Clinician Access Required)
    |--------------------------------------------------------------------------
    */

    Route::middleware(['auth', 'verified', 'clinician'])->group(function () {
        // Consultation Management Pages
        Route::get('consultations', function () {
            return Inertia::render('Consultations/Index');
        })->name('consultations.index');

        Route::get('consultations/create', function () {
            return Inertia::render('Consultations/Consultation');
        })->name('consultations.create');

        Route::get('consultations/create/{patientId}', function ($patientId) {
            return Inertia::render('Consultations/Consultation', ['patientId' => $patientId]);
        })->name('consultations.create.patient');

        // Template route MUST come before parameterized routes
        Route::get('consultations/template', [ConsultationController::class, 'getTemplate'])->name('consultations.template');

        // Consultation Dashboard
        Route::get('consultation-dashboard/{patientId}', function ($patientId) {
            return Inertia::render('Consultations/Dashboard', ['patientId' => $patientId]);
        })->name('consultation.dashboard');

        Route::get('consultations/{id}', function ($id) {
            return Inertia::render('Consultations/Show', ['consultationId' => $id]);
        })->name('consultations.show');

        Route::get('consultations/{id}/edit', function ($id) {
            return Inertia::render('Consultations/Consultation', ['consultationId' => $id]);
        })->name('consultations.edit');

        Route::get('consultations/from-appointment/{appointmentId}', function ($appointmentId) {
            return Inertia::render('Consultations/Consultation', ['appointmentId' => $appointmentId]);
        })->name('consultations.from-appointment');

        // Prescriptions Pages
        Route::get('prescriptions', function () {
            return Inertia::render('Prescriptions/Index');
        })->name('prescriptions.index');

        // Medical Letters Pages
        Route::get('medical-letters', function () {
            return Inertia::render('MedicalLetters/Index');
        })->name('medical-letters.index');

        // Doctor Settings Pages
        Route::get('doctor-settings', function () {
            return Inertia::render('DoctorSettings/Index');
        })->name('doctor-settings.index');
    });

    // Clinical routes (protected by clinician middleware) - for session-based authentication
    Route::middleware(['auth', 'verified', 'clinician'])->group(function () {
        // Consultation API routes
        Route::get('consultations-list', [ConsultationController::class, 'index'])->name('consultations.api.index');
        Route::prefix('consultations')->name('consultations.api.')->group(function () {
            Route::post('/', [ConsultationController::class, 'store'])->name('store');
            Route::get('/{id}', [ConsultationController::class, 'show'])->name('show');
            Route::put('/{id}', [ConsultationController::class, 'update'])->name('update');
            Route::delete('/{id}', [ConsultationController::class, 'destroy'])->name('destroy');
            Route::post('/analyze-transcript', [ConsultationController::class, 'analyzeTranscript'])->name('analyze-transcript');
            Route::post('/from-appointment/{appointmentId}', [ConsultationController::class, 'createFromAppointment'])->name('from-appointment');
            Route::post('/{id}/send-summary', [ConsultationController::class, 'sendSummary'])->name('send-summary');
        });

        // Consultation files
        Route::get('consultations/{id}/files', [ConsultationDocumentController::class, 'index']);
        Route::post('consultations/{id}/files', [ConsultationDocumentController::class, 'store']);
        Route::put('consultation-files/{id}', [ConsultationDocumentController::class, 'update']);
        Route::delete('consultation-files/{id}', [ConsultationDocumentController::class, 'destroy']);
        Route::get('consultation-files/{id}/download', [ConsultationDocumentController::class, 'download']);

        // Document management features
        Route::post('get-document-access-key', [ConsultationDocumentController::class, 'getDocumentAccessKey']);
        Route::post('create-mobile-upload-session', [ConsultationDocumentController::class, 'createMobileUploadSession']);
        Route::get('check-mobile-upload-session', [ConsultationDocumentController::class, 'checkMobileUploadSession']);


        // Consultation Notes routes
        Route::get('consultations/{consultationId}/notes', [ConsultationNoteController::class, 'index']);
        Route::post('consultations/{consultationId}/notes', [ConsultationNoteController::class, 'store']);
        Route::put('consultations/{consultationId}/notes/{noteId}', [ConsultationNoteController::class, 'update']);
        Route::delete('consultations/{consultationId}/notes/{noteId}', [ConsultationNoteController::class, 'destroy']);

        // Diagnosis routes
        Route::get('consultations/{consultationId}/diagnoses', [DiagnosisController::class, 'index']);
        Route::post('consultations/{consultationId}/diagnoses', [DiagnosisController::class, 'store']);
        Route::put('consultations/{consultationId}/diagnoses/{diagnosisId}', [DiagnosisController::class, 'update']);
        Route::delete('consultations/{consultationId}/diagnoses/{diagnosisId}', [DiagnosisController::class, 'destroy']);
        Route::get('diagnoses/search', [DiagnosisController::class, 'search']);

        // Medications routes
        Route::get('medications', [MedicationController::class, 'index']);
        Route::get('medications/search', [MedicationController::class, 'search']);
        Route::get('medications/drug-classes', [MedicationController::class, 'drugClasses']);
        Route::get('medications/forms', [MedicationController::class, 'forms']);
        Route::get('medications/routes', [MedicationController::class, 'routes']);
        Route::post('medications/check-interactions', [MedicationController::class, 'checkInteractions']);
        Route::get('medications/{id}', [MedicationController::class, 'show']);

        // Prescriptions API routes (using -list suffix to avoid conflict with page route)
        Route::get('prescriptions-list', [PrescriptionController::class, 'index']);
        Route::post('prescriptions', [PrescriptionController::class, 'store']);
        Route::get('prescriptions/{id}', [PrescriptionController::class, 'show']);
        Route::put('prescriptions/{id}', [PrescriptionController::class, 'update']);
        Route::delete('prescriptions/{id}', [PrescriptionController::class, 'destroy']);
        Route::post('prescriptions/{id}/send', [PrescriptionController::class, 'send']);
        Route::get('prescription-refills', [PrescriptionRefillController::class, 'index']);
        Route::post('prescription-refills', [PrescriptionRefillController::class, 'store']);
        Route::put('prescription-refills/{id}', [PrescriptionRefillController::class, 'update']);

        // Consultation Prescriptions API routes
        Route::prefix('consultation-prescriptions')->name('consultation-prescriptions.')->group(function () {
            Route::get('/', [\App\Http\Controllers\ConsultationPrescriptionController::class, 'index'])->name('index');
            Route::post('/', [\App\Http\Controllers\ConsultationPrescriptionController::class, 'store'])->name('store');
            Route::put('/{id}', [\App\Http\Controllers\ConsultationPrescriptionController::class, 'update'])->name('update');
            Route::delete('/{id}', [\App\Http\Controllers\ConsultationPrescriptionController::class, 'destroy'])->name('destroy');
        });

        // Consultation Letterhead routes
        Route::get('consultations/{id}/prescriptions-letterhead', [\App\Http\Controllers\ConsultationPrescriptionController::class, 'viewAllWithLetterhead'])->name('consultations.prescriptions-letterhead');

        // Patient vitals routes
        Route::get('patients/{patientId}/vitals', [PatientController::class, 'getVitals']);
        Route::post('patients/{patientId}/vitals', [PatientController::class, 'storeVitals']);

        // Treatment Plans routes
        Route::get('consultations/{consultationId}/treatment-plans', [TreatmentPlanController::class, 'index']);
        Route::post('consultations/{consultationId}/treatment-plans', [TreatmentPlanController::class, 'store']);
        Route::put('consultations/{consultationId}/treatment-plans/{planId}', [TreatmentPlanController::class, 'update']);
        Route::delete('consultations/{consultationId}/treatment-plans/{planId}', [TreatmentPlanController::class, 'destroy']);
        Route::post('consultations/{consultationId}/treatment-plans/{planId}/complete', [TreatmentPlanController::class, 'markCompleted']);
        Route::get('treatment-plans/due-for-review', [TreatmentPlanController::class, 'dueForReview']);

        // Doctor Settings API routes (data only)
        Route::get('doctor-settings-data', [DoctorSettingsController::class, 'show']);
        Route::put('doctor-settings', [DoctorSettingsController::class, 'update']);

        // Medical Letters routes (simplified - letters are saved as consultation documents)
        Route::get('letter-templates/available', [MedicalLetterController::class, 'getAvailableTemplates']);
        Route::put('medical-letters/{id}', [MedicalLetterController::class, 'update']);
        Route::post('medical-letters/generate-from-template', [MedicalLetterController::class, 'generateFromTemplate']);
        Route::post('medical-letters/process-template', [MedicalLetterController::class, 'processTemplate']);
        Route::post('medical-letters/generate-with-letterhead', [MedicalLetterController::class, 'generateLetterWithLetterhead']);

        // Prescription letterhead routes
        Route::get('prescriptions/{id}/view-letterhead', [PrescriptionController::class, 'viewWithLetterhead']);
        Route::post('prescriptions/{id}/generate-pdf', [PrescriptionController::class, 'generatePDF']);
        Route::get('prescriptions/{id}/download-pdf', [PrescriptionController::class, 'downloadPDF']);
        Route::post('prescriptions/{id}/email', [PrescriptionController::class, 'emailPrescription']);
        Route::post('prescriptions/{id}/share', [PrescriptionController::class, 'createShareLink']);

        // Letterhead settings routes
        Route::get('letterhead/settings', [LetterheadController::class, 'getSettings']);
        Route::post('letterhead/settings', [LetterheadController::class, 'updateSettings']);
        Route::get('letterhead/suggestions', [LetterheadController::class, 'getSmartSuggestions']);
        Route::get('letterhead/auto-detect', [LetterheadController::class, 'autoDetectChanges']);
        Route::get('letterhead/field-suggestions', [LetterheadController::class, 'getFieldSuggestions']);
        Route::post('letterhead/upload-header-image', [LetterheadController::class, 'uploadHeaderImage']);
        Route::post('letterhead/upload-footer-image', [LetterheadController::class, 'uploadFooterImage']);
        Route::delete('letterhead/remove-header-image', [LetterheadController::class, 'removeHeaderImage']);
        Route::delete('letterhead/remove-footer-image', [LetterheadController::class, 'removeFooterImage']);

        // Consultation AI and Recording routes
        Route::prefix('consultations')->name('consultations.')->group(function () {
            Route::post('/transcribe-audio', [ConsultationController::class, 'transcribeAudio'])->name('transcribe-audio');
            Route::post('/ai-populate', [ConsultationController::class, 'aiPopulateRecords'])->name('ai-populate');

            // Mobile recording routes
            Route::prefix('mobile-recording')->name('mobile-recording.')->group(function () {
                Route::post('/create', [ConsultationController::class, 'createMobileRecordingSession'])->name('create');
                Route::post('/status', [ConsultationController::class, 'checkMobileRecordingStatus'])->name('status');
                Route::post('/process', [ConsultationController::class, 'processMobileRecording'])->name('process');
            });
        });

        // Consultation vitals routes
        Route::get('consultations/{consultationId}/vitals/recent/{patientId}', [ConsultationTabController::class, 'getRecentVitals'])->name('consultations.vitals.recent');

        // Instagram refresh media route
        Route::post('instagram/refresh-media', [InstagramController::class, 'refreshMedia']);

        // ICD-10 API routes
        Route::prefix('icd10')->group(function () {
            Route::get('search', [Icd10Controller::class, 'search']);
            Route::get('chapters', [Icd10Controller::class, 'chapters']);
            Route::get('chapter/{chapter}', [Icd10Controller::class, 'byChapter']);
            Route::get('popular', [Icd10Controller::class, 'popular']);
            Route::get('{id}', [Icd10Controller::class, 'show']);
        });

        // Provider Availability for Follow-up Scheduling
        Route::get('providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getProviderAvailableSlots']);

        // Appointments and Patients routes (needed for consultation creation)
        Route::get('appointments/{id}', [AppointmentController::class, 'show']);
        Route::get('patients/{id}', [PatientController::class, 'show']);
        Route::get('patients/{id}/consultations', [ManagementController::class, 'patientConsultations']);
    });
});

    // Patient preferences routes
    Route::get('patient/appointment-preferences', [PatientPreferencesController::class, 'getAppointmentPreferences']);
    Route::put('patient/appointment-preferences', [PatientPreferencesController::class, 'updateAppointmentPreferences']);

    // Provider registration route
    Route::post('provider/register', [ProviderRegistrationController::class, 'register']);

    // Provider routes
    Route::get('providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);

    // User route for CSRF validation
    Route::get('user', [UserController::class, 'show']);

    // Management dashboard routes (KPI route handled by AnalyticsController above)



    // Clinics management routes
    Route::get('clinics', [ManagementController::class, 'clinics'])->name('clinics')->middleware('permission:view clinics');
    Route::get('clinics-list', [ClinicController::class, 'index'])->middleware('permission:view clinics');
    Route::get('clinics-list-dropdown', [ClinicController::class, 'getClinicsList']);
    Route::post('save-clinic', [ClinicController::class, 'store']);
    Route::get('get-clinic/{id}', [ClinicController::class, 'show']);
    Route::put('update-clinic/{id}', [ClinicController::class, 'update']);
    Route::delete('delete-clinic/{id}', [ClinicController::class, 'destroy']);
    Route::get('clinic-stats/{id}', [ClinicController::class, 'getStats']);
    Route::get('clinic-users/{id}', [ClinicController::class, 'getUsers']);

    // Service Categories management route (Vue component)
    Route::get('categories', [CategoryController::class, 'indexAll'])->name('categories.index')->middleware('permission:view categories');

    // Categories API routes for Vue component
    Route::get('service-categories-list', [CategoryController::class, 'getCategoriesList'])->middleware('permission:view categories');
    Route::post('categories-api', [CategoryController::class, 'apiStore'])->middleware('permission:create categories');
    Route::put('categories-api/{id}', [CategoryController::class, 'apiUpdate'])->middleware('permission:edit categories');
    Route::delete('categories-api/{id}', [CategoryController::class, 'apiDestroy'])->middleware('permission:delete categories');
    Route::patch('categories-api/{id}/toggle-status', [CategoryController::class, 'apiToggleStatus'])->middleware('permission:edit categories');

    // Categories management routes (clinic-specific API)
    Route::get('clinics/{clinicId}/categories', [CategoryController::class, 'index']);
    Route::post('clinics/{clinicId}/categories', [CategoryController::class, 'store']);
    Route::get('clinics/{clinicId}/categories/{id}', [CategoryController::class, 'show']);
    Route::put('clinics/{clinicId}/categories/{id}', [CategoryController::class, 'update']);
    Route::delete('clinics/{clinicId}/categories/{id}', [CategoryController::class, 'destroy']);
    Route::get('clinics/{clinicId}/categories-active', [CategoryController::class, 'getActiveCategories']);

    // Payments management routes
    Route::get('payments-list', [PaymentController::class, 'index']);
    Route::get('payments-detail/{id}', [PaymentController::class, 'show']);
    Route::post('payments/{id}/refund', [PaymentController::class, 'processRefund']);

    // Chats management routes
    Route::get('chats-list', [ChatController::class, 'managementIndex']);
    Route::get('chats-stats', [ChatController::class, 'getStats']);
    Route::get('chats-detail/{id}', [ChatController::class, 'managementShow']);
    Route::get('chats-messages/{id}', [ChatController::class, 'getMessages']);
    Route::post('chats-flag/{id}', [ChatController::class, 'flagChat']);
    Route::post('chats-unflag/{id}', [ChatController::class, 'unflagChat']);
    Route::post('chats-archive/{id}', [ChatController::class, 'archiveChat']);
    Route::post('chats-add-message/{id}', [ChatController::class, 'addManagementMessage']);

    // Permissions management routes
    Route::get('roles-list', [PermissionController::class, 'getRoles']);
    Route::get('permissions-list', [PermissionController::class, 'index']);
    Route::post('roles', [PermissionController::class, 'createRole']);
    Route::put('roles/{id}', [PermissionController::class, 'updateRole']);
    Route::delete('roles/{id}', [PermissionController::class, 'deleteRole']);
    Route::post('assign-permissions', [PermissionController::class, 'assignPermissions']);

    // Impersonation routes
    Route::middleware('role:admin')->group(function () {
        Route::post('impersonate/{userId}', [ImpersonationController::class, 'start'])->name('impersonate.start');
    });

    // These routes need to be accessible to all authenticated users (including impersonated users)
    Route::middleware('auth')->group(function () {
        Route::post('stop-impersonation', [ImpersonationController::class, 'stop'])->name('impersonate.stop');
        Route::get('impersonation-status', [ImpersonationController::class, 'status'])->name('impersonate.status');
    });

    // Approval routes (admin only)
    Route::middleware('role:admin')->group(function () {
        // Product approval routes
        Route::get('pending-products', [ProductManagementController::class, 'getPendingProducts'])->name('admin.products.pending');
        Route::post('approve-product/{id}', [ProductManagementController::class, 'approveProduct'])->name('admin.products.approve');
        Route::post('reject-product/{id}', [ProductManagementController::class, 'rejectProduct'])->name('admin.products.reject');

        // Service approval routes
        Route::get('pending-services', [ServiceController::class, 'getPendingServices'])->name('admin.services.pending');
        Route::post('approve-service/{id}', [ServiceController::class, 'approveService'])->name('admin.services.approve');
        Route::post('reject-service/{id}', [ServiceController::class, 'rejectService'])->name('admin.services.reject');
    });

    // Email templates routes (session-based authentication for web app)
    Route::get('email-templates-list', [EmailTemplateController::class, 'index']);
    Route::get('email-templates/{id}', [EmailTemplateController::class, 'show']);
    Route::post('email-templates', [EmailTemplateController::class, 'store']);
    Route::put('email-templates/{id}', [EmailTemplateController::class, 'update']);
    Route::delete('email-templates/{id}', [EmailTemplateController::class, 'destroy']);


    // System verification routes
    Route::post('verify-transaction-system', [SystemVerificationController::class, 'verifyTransactionSystem']);
    Route::post('verify-anonymous-chat-mapping', [SystemVerificationController::class, 'verifyAnonymousChatMapping']);

    // Notifications routes (session-based authentication for web app)
    Route::get('notifications-list', [NotificationTemplateController::class, 'index']);
    Route::get('notifications/{id}', [NotificationTemplateController::class, 'show']);
    Route::put('notifications/{id}', [NotificationTemplateController::class, 'update']);

    // Enhanced notification management routes
    Route::post('notifications/send-by-device-type', [NotificationManagementController::class, 'sendByDeviceType']);
    Route::post('notifications/send-by-browser', [NotificationManagementController::class, 'sendByBrowser']);
    Route::post('notifications/send-by-platform', [NotificationManagementController::class, 'sendByPlatform']);

    // Referrals routes (session-based authentication for web app)
    Route::get('referrals-code', [ReferralController::class, 'getReferralCode']);
    Route::post('send-referral-invite', [ReferralController::class, 'createReferral']);
    Route::get('referrals-my', [ReferralController::class, 'getUserReferrals']);
    Route::get('referrals-list', [ReferralController::class, 'index']);
    Route::get('referrals-stats', [ReferralController::class, 'getStats']);

    // Credits routes (session-based authentication for web app)
    Route::get('credits-list', [UserCreditController::class, 'index']);
    Route::get('credits-balance', [UserCreditController::class, 'getCreditBalance']);
    Route::get('credits-transactions', [UserCreditController::class, 'getTransactionHistory']);
    Route::get('credits-all-transactions', [UserCreditController::class, 'getAllCreditTransactions']);
    Route::get('credits-stats', [UserCreditController::class, 'getCreditsStats']);
    Route::post('credits-add', [UserCreditController::class, 'addCredits']);
    Route::get('users-search', [UserController::class, 'index']);

    // Clubs routes (session-based authentication for web app)
    Route::get('clubs-list', [ClubManagementController::class, 'getClubMembers']);
    Route::get('clubs-stats', [ClubManagementController::class, 'getClubStats']);
    Route::get('clubs-codes', [ClubManagementController::class, 'getFounderCodes']);

    // Waitlist management routes
    Route::get('waitlist-stats', [AdminWaitlistController::class, 'getStats']);
    Route::get('waitlist-analytics', [AdminWaitlistController::class, 'getSignupAnalytics']);
    Route::post('waitlist-toggle', [AdminWaitlistController::class, 'toggleWaitlistMode']);

    // Admin ecommerce API routes (session-based authentication for web app)
    Route::prefix('admin')->group(function () {
        // Bulk import routes (must be before products/{id} routes)
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate']);
        Route::get('products/import-instructions', [ProductManagementController::class, 'downloadImportInstructions']);
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport']);
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport']);

        // Product management API
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Product category management API
        Route::get('categories-list', [ProductCategoryController::class, 'index']);
        Route::post('save-category', [ProductCategoryController::class, 'store']);
        Route::put('save-category/{id}', [ProductCategoryController::class, 'update']);
        Route::delete('delete-category/{id}', [ProductCategoryController::class, 'destroy']);
        Route::patch('toggle-category-status/{id}', [ProductCategoryController::class, 'toggleStatus']);

        // Order management API
        Route::get('orders-list', [OrderManagementController::class, 'index']);
        Route::get('order-details/{id}', [OrderManagementController::class, 'show']);
        Route::patch('update-order-status/{id}', [OrderManagementController::class, 'updateStatus']);
        Route::patch('update-payment-status/{id}', [OrderManagementController::class, 'updatePaymentStatus']);
        Route::post('add-order-note/{id}', [OrderManagementController::class, 'addNote']);
        Route::get('order-stats', [OrderManagementController::class, 'getOrderStats']);
        Route::get('export-orders', [OrderManagementController::class, 'exportOrders']);
    });

    // Waitlist invitation management routes
    Route::get('waitlist-requests', [WaitlistController::class, 'getWaitlistRequests']);
    Route::post('waitlist-requests/{requestId}/invite', [WaitlistController::class, 'sendInvitation']);
    Route::post('waitlist-requests/bulk-invite', [WaitlistController::class, 'sendBulkInvitations']);
    Route::post('send-bulk-invitations', [WaitlistController::class, 'sendBulkInvitations']);
    Route::get('waitlist-invitation-stats', [WaitlistController::class, 'getInvitationStats']);

    // Club invitation routes
    Route::post('send-club-invitation', [ClubManagementController::class, 'sendClubInvitation']);
    Route::post('send-bulk-club-invitations', [ClubManagementController::class, 'sendBulkClubInvitations']);

    // Provider availability API routes
    Route::middleware(['auth', 'verified'])->prefix('provider')->group(function () {
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::post('save-availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);
        Route::put('save-availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);

        // Provider absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);

        // Provider dashboard data
        Route::get('get-dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('get-appointments', [ProviderController::class, 'getAppointments']);
        Route::get('get-patients', [ProviderController::class, 'getPatients']);
        Route::get('search-patients', [ProviderController::class, 'searchPatients']);
        Route::post('create-patient', [ProviderController::class, 'createPatient']);



        // Patient-Provider assignment routes (for clinic admins and admins)
        Route::post('assign-patient-to-provider', [ProviderController::class, 'assignPatientToProvider']);
        Route::post('remove-patient-from-provider', [ProviderController::class, 'removePatientFromProvider']);
        Route::get('patient/{patientId}/providers', [ProviderController::class, 'getPatientProviders']);



        // Provider profile
        Route::get('get-profile', [ProviderController::class, 'getProfile']);
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);



        // Provider product management API (providers can manage their own products by default)
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Provider earnings
        Route::get('get-earnings', [ProviderController::class, 'getDashboardData']);
    });

    // Management dashboard API routes - KPI route is defined earlier without permission restriction

    // Appointment management routes (following availability and services pattern)
    Route::get('appointments-list', [AppointmentController::class, 'index']);
    Route::get('get-appointments', [AppointmentController::class, 'userAppointments']);
    Route::post('save-appointment', [AppointmentController::class, 'store']);
    Route::put('save-appointment/{id}', [AppointmentController::class, 'update']);
    Route::delete('delete-appointment/{id}', [AppointmentController::class, 'destroy'])->middleware('permission:delete appointments');

    // Debug route for troubleshooting appointment access
    Route::get('debug-appointment-access', [AppointmentController::class, 'debugUserAccess']);

    // Enhanced appointment management routes
    Route::get('appointments/index', [AppointmentController::class, 'index'])->middleware('permission:view appointments');
    Route::post('appointments/bulk-update-status', [AppointmentController::class, 'bulkUpdateStatus'])->middleware('permission:edit appointments');
    Route::post('appointments/bulk-delete', [AppointmentController::class, 'bulkDelete'])->middleware('permission:delete appointments');
    Route::get('appointments/export', [AppointmentController::class, 'exportAppointments'])->middleware('permission:view appointments');
    Route::post('appointments/{id}/status', [AppointmentController::class, 'updateStatus'])->middleware('permission:edit appointments');

    // Check-in/Check-out routes
    Route::post('appointments/{id}/check-in', [AppointmentController::class, 'checkIn'])->middleware('permission:edit appointments');
    Route::post('appointments/{id}/check-out', [AppointmentController::class, 'checkOut'])->middleware('permission:edit appointments');
    Route::post('appointments/{id}/reschedule', [AppointmentController::class, 'reschedule'])->middleware('permission:edit appointments');

    // Consultation creation from appointment (clinicians only)
    Route::post('consultations/from-appointment/{appointmentId}', [ConsultationController::class, 'createFromAppointment']);

    // Dropdown data routes for appointments
    Route::get('providers-list', [ProviderController::class, 'getProvidersList']);
    Route::get('patients-list-dropdown', [PatientController::class, 'getPatientsListDropdown']);

    // Patients management routes
    Route::get('patients-list', [PatientController::class, 'getPatientsList']);
    Route::post('save-patient', [PatientController::class, 'store']);
    Route::put('update-patient/{id}', [PatientController::class, 'update']);
    Route::delete('delete-patient/{id}', [PatientController::class, 'destroy']);
    Route::get('patients/{id}/consultations-list', [PatientController::class, 'getPatientConsultations']);
    Route::get('patients/{id}/appointments-list', [PatientController::class, 'getAppointmentHistory']);

    // Provider routes (following standard pattern)
    Route::get('get-providers/{id}/services', [ServiceController::class, 'index']);
    Route::get('get-providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);

    // Payment routes (following standard pattern) - moved inside auth middleware
    Route::post('save-appointment-with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('process-web-payment', [PaymentController::class, 'processWebPayment']);
    Route::post('confirm-elements-payment', [PaymentController::class, 'confirmElementsPayment']);

    // Checkout flow routes
    Route::post('checkout/config', [\App\Http\Controllers\CheckoutController::class, 'getCheckoutConfig']);
    Route::post('checkout/appointment', [\App\Http\Controllers\CheckoutController::class, 'processAppointment']);
    Route::post('checkout/product', [\App\Http\Controllers\CheckoutController::class, 'processProduct']);

    // Debug route to clear test country session
    Route::post('debug/clear-test-country', function() {
        session()->forget('test_country_code');
        return response()->json(['success' => true, 'message' => 'Test country session cleared']);
    });

    // Countries API for public use
    Route::get('countries-list', [\App\Http\Controllers\Admin\CountryController::class, 'getActiveCountries']);

    // Test endpoint for debugging
    Route::post('test-appointment-validation', function(\Illuminate\Http\Request $request) {
        \Log::info('Test endpoint called with data:', $request->all());

        // Check if provider exists
        $provider = \App\Models\Provider::find($request->provider_id);
        $service = \App\Models\Service::find($request->service_id);

        return response()->json([
            'message' => 'Test endpoint reached',
            'user_authenticated' => auth()->check(),
            'user_id' => auth()->id(),
            'provider_exists' => !!$provider,
            'service_exists' => !!$service,
            'provider_data' => $provider ? $provider->toArray() : null,
            'service_data' => $service ? $service->toArray() : null,
            'request_data' => $request->all()
        ]);
    });

    Route::get('api/appointments/{id}', [AppointmentController::class, 'show']);

    // Ecommerce routes (session-based authentication for web app)
    Route::prefix('shop')->group(function () {
        // Product routes
        Route::get('products', [ProductController::class, 'index']);
        Route::get('products/{slug}', [ProductController::class, 'show']);
        Route::get('categories', [ProductController::class, 'categories']);
        Route::get('featured-products', [ProductController::class, 'featured']);
        Route::get('search-products', [ProductController::class, 'search']);

        // Product review routes
        Route::get('products/{product}/reviews', [ProductReviewController::class, 'index']);
        Route::middleware('auth')->group(function () {
            Route::post('products/{product}/reviews', [ProductReviewController::class, 'store']);
            Route::put('products/{product}/reviews/{review}', [ProductReviewController::class, 'update']);
            Route::delete('products/{product}/reviews/{review}', [ProductReviewController::class, 'destroy']);
            Route::get('products/{product}/can-review', [ProductReviewController::class, 'canReview']);
        });

        // Shopping cart routes
        Route::get('cart', [ShoppingCartController::class, 'index']);
        Route::post('cart/add', [ShoppingCartController::class, 'add']);
        Route::put('cart/{productId}', [ShoppingCartController::class, 'update']);
        Route::delete('cart/{productId}', [ShoppingCartController::class, 'remove']);
        Route::delete('cart', [ShoppingCartController::class, 'clear']);
        Route::get('cart/count', [ShoppingCartController::class, 'count']);

        // Order routes
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{orderNumber}', [OrderController::class, 'show']);
        Route::post('orders/{orderNumber}/cancel', [OrderController::class, 'cancel']);
        Route::get('checkout', [OrderController::class, 'showCheckout']);
        Route::post('checkout', [OrderController::class, 'checkout']);
        Route::post('orders/{orderNumber}/confirm-payment', [OrderController::class, 'confirmPayment']);

        // Digital downloads
        Route::get('downloads', [DigitalDownloadController::class, 'index']);
        Route::get('downloads/user', [DigitalDownloadController::class, 'userDownloads']);
        Route::get('downloads/{token}', [DigitalDownloadController::class, 'show']);
    });

    // Digital download route already defined above - removing duplicate

    // Instagram Integration Routes
    Route::get('auth/instagram', [InstagramAuthController::class, 'redirectToInstagram'])->name('instagram.auth');
    Route::post('instagram/disconnect', [InstagramAuthController::class, 'disconnect'])->name('instagram.disconnect');
    Route::post('instagram/sync', [InstagramAuthController::class, 'syncContent'])->name('instagram.sync');

    // Instagram API routes (session-based authentication)
    Route::prefix('instagram')->group(function () {
        Route::get('auth-url', [InstagramController::class, 'getAuthUrl']);
        Route::get('account-status', [InstagramController::class, 'getAccountStatus']);
        Route::get('connection-progress', [InstagramController::class, 'getConnectionProgress']);
        Route::post('disconnect', [InstagramController::class, 'disconnect']);
        Route::post('sync', [InstagramController::class, 'syncContent']);
        Route::post('refresh-token', [InstagramController::class, 'refreshToken']);
        Route::get('feed-content', [InstagramController::class, 'getFeedContent']);
        Route::get('stories', [InstagramController::class, 'getStories']);
    });

// Anonymous chat routes already defined above - removing duplicate

// PUBLIC FEED ROUTES already defined above - removing duplicate



// Bot Management Routes (Admin Only)
Route::middleware(['auth'])->prefix('admin/bot-management')->name('admin.bot-management.')->group(function () {
    // Temporarily removed role:admin middleware for testing
    Route::get('/', [\App\Http\Controllers\Admin\BotManagementController::class, 'dashboard'])->name('dashboard');
    Route::post('/generate-all', [\App\Http\Controllers\Admin\BotManagementController::class, 'generateAllPosts'])->name('generate-all');
    Route::post('/generate/{persona}', [\App\Http\Controllers\Admin\BotManagementController::class, 'generatePersonaPost'])->name('generate-persona');
    Route::get('/analytics', [\App\Http\Controllers\Admin\BotManagementController::class, 'analytics'])->name('analytics');
    Route::get('/post/{post}/details', [\App\Http\Controllers\Admin\BotManagementController::class, 'getPostDetails'])->name('post-details');
    Route::get('/test-system', [\App\Http\Controllers\Admin\BotManagementController::class, 'testSystem'])->name('test-system');
    Route::get('/job-progress', [\App\Http\Controllers\Admin\BotManagementController::class, 'getJobProgress'])->name('job-progress');
    Route::get('/pending-jobs', [\App\Http\Controllers\Admin\BotManagementController::class, 'getPendingJobs'])->name('pending-jobs');
    Route::post('/cancel-jobs', [\App\Http\Controllers\Admin\BotManagementController::class, 'cancelJobs'])->name('cancel-jobs');
    Route::post('/cancel-specific-job', [\App\Http\Controllers\Admin\BotManagementController::class, 'cancelSpecificJob'])->name('cancel-specific-job');
    Route::post('/delete-failed-post', [\App\Http\Controllers\Admin\BotManagementController::class, 'deleteFailedPost'])->name('delete-failed-post');
    Route::get('/scheduled-posts', [\App\Http\Controllers\Admin\BotManagementController::class, 'getScheduledPosts'])->name('scheduled-posts');
    Route::post('/update-settings', [\App\Http\Controllers\Admin\BotManagementController::class, 'updateSettings'])->name('update-settings');
    Route::post('/trigger-scheduling', [\App\Http\Controllers\Admin\BotManagementController::class, 'triggerScheduling'])->name('trigger-scheduling');
    Route::post('/clear-cache', [\App\Http\Controllers\Admin\BotManagementController::class, 'clearCache'])->name('clear-cache');
    Route::get('/real-time-stats', [\App\Http\Controllers\Admin\BotManagementController::class, 'getRealTimeStats'])->name('real-time-stats');
});

/*
|--------------------------------------------------------------------------
| Billing Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['auth', 'verified'])->group(function () {
    // Tax Management Routes
    Route::prefix('taxes')->name('taxes.')->group(function () {
        Route::get('/', [TaxController::class, 'index'])->name('index');
        Route::get('/active', [TaxController::class, 'active'])->name('active');
        Route::post('/', [TaxController::class, 'store'])->name('store');
        Route::get('/{tax}', [TaxController::class, 'show'])->name('show');
        Route::put('/{tax}', [TaxController::class, 'update'])->name('update');
        Route::delete('/{tax}', [TaxController::class, 'destroy'])->name('destroy');
    });

    // Bill Management Routes
    Route::prefix('bills')->name('bills.')->group(function () {
        Route::get('/', [BillController::class, 'index'])->name('index');
        Route::get('/list', [BillController::class, 'getBillsList'])->name('list');
        Route::get('/create', [BillController::class, 'create'])->name('create');
        Route::post('/', [BillController::class, 'store'])->name('store');
        Route::get('/unpaid', [BillController::class, 'unpaid'])->name('unpaid');
        Route::get('/stats', [BillController::class, 'stats'])->name('stats');
        Route::get('/report', [BillController::class, 'report'])->name('report');
        Route::get('/unbilled-services', [BillController::class, 'unbilledServices'])->name('unbilled-services');
        Route::post('/create-from-consultation', [BillController::class, 'createFromConsultation'])->name('create-from-consultation');
        Route::get('/{bill}', [BillController::class, 'show'])->name('show');
        Route::put('/{bill}', [BillController::class, 'update'])->name('update');
        Route::delete('/{bill}', [BillController::class, 'destroy'])->name('destroy');
        Route::post('/{bill}/mark-paid', [BillController::class, 'markAsPaid'])->name('mark-paid');
        Route::post('/{bill}/send-to-patient', [BillController::class, 'sendToPatient'])->name('send-to-patient');
        Route::get('/{bill}/download-invoice', [BillController::class, 'downloadInvoice'])->name('download-invoice');
        Route::get('/{bill}/preview-invoice', [BillController::class, 'previewInvoice'])->name('preview-invoice');
        Route::post('/{bill}/generate-invoice', [BillController::class, 'generateInvoice'])->name('generate-invoice');
        Route::get('/{bill}/download-receipt', [BillController::class, 'downloadReceipt'])->name('download-receipt');
        Route::post('/{bill}/send-invoice-email', [BillController::class, 'sendInvoiceEmail'])->name('send-invoice-email');
        Route::post('/send-bulk-emails', [BillController::class, 'sendBulkEmails'])->name('send-bulk-emails');
        Route::post('/send-overdue-reminders', [BillController::class, 'sendOverdueReminders'])->name('send-overdue-reminders');
        Route::get('/email-stats', [BillController::class, 'emailStats'])->name('email-stats');

        // Bill Items Routes
        Route::prefix('{bill}/items')->name('items.')->group(function () {
            Route::post('/', [BillItemController::class, 'store'])->name('store');
            Route::put('/{billItem}', [BillItemController::class, 'update'])->name('update');
            Route::delete('/{billItem}', [BillItemController::class, 'destroy'])->name('destroy');
        });

        // Bill Payment Routes
        Route::prefix('{bill}/payment')->name('payment.')->group(function () {
            Route::post('/intent', [PaymentController::class, 'createBillPaymentIntent'])->name('intent');
            Route::post('/process', [PaymentController::class, 'processBillPayment'])->name('process');
        });
    });

        // Bill API Routes
    Route::get('/get-bill-details/{billId}', [BillController::class, 'getBillDetails'])->name('bills.details');


    // Consultation Services Routes
    Route::prefix('consultations/{consultation}/services')->name('consultations.services.')->group(function () {
        Route::get('/', [ConsultationServiceController::class, 'index'])->name('index');
        Route::post('/', [ConsultationServiceController::class, 'store'])->name('store');
        Route::get('/unbilled', [ConsultationServiceController::class, 'unbilled'])->name('unbilled');
        Route::post('/generate-bill', [ConsultationServiceController::class, 'generateBill'])->name('generate-bill');
        Route::post('/consultation-fee', [ConsultationServiceController::class, 'addConsultationFee'])->name('consultation-fee');
        Route::post('/bulk-add', [ConsultationServiceController::class, 'bulkAdd'])->name('bulk-add');
        Route::put('/{consultationService}', [ConsultationServiceController::class, 'update'])->name('update');
        Route::delete('/{consultationService}', [ConsultationServiceController::class, 'destroy'])->name('destroy');
    });

    // Consultation Sharing Routes
    Route::prefix('consultation-sharing')->name('consultation-sharing.')->group(function () {
        Route::post('/documents/{document}/share', [ConsultationSharingController::class, 'shareDocument'])->name('documents.share');
        Route::post('/prescriptions/{prescription}/share', [ConsultationSharingController::class, 'sharePrescription'])->name('prescriptions.share');
        Route::post('/medical-letters/{letter}/share', [ConsultationSharingController::class, 'shareMedicalLetter'])->name('medical-letters.share');
        Route::post('/consultations/{consultation}/share', [ConsultationSharingController::class, 'shareConsultationSummary'])->name('consultations.share');
        Route::get('/consultations/{consultation}/contacts', [ConsultationSharingController::class, 'getPatientContacts'])->name('consultations.contacts');
    });

    // Public bill payment page (accessible with token)
    Route::get('/bills/{bill}/pay', [BillController::class, 'showPaymentPage'])->name('bills.payment.page');
});

/*
|--------------------------------------------------------------------------
| Include Additional Route Files
|--------------------------------------------------------------------------
*/

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// TDL Labs Integration Routes
Route::middleware(['auth', 'verified'])->group(function () {

    // Test Catalog Routes (accessible to all authenticated users)
    Route::prefix('lab/catalog')->group(function () {
        Route::get('/', [TdlTestCatalogController::class, 'index'])->name('lab.catalog.index');
        Route::get('/active', [TdlTestCatalogController::class, 'active'])->name('lab.catalog.active');
        Route::get('/categories', [TdlTestCatalogController::class, 'categories'])->name('lab.catalog.categories');
        Route::get('/search', [TdlTestCatalogController::class, 'search'])->name('lab.catalog.search');
        Route::get('/stats', [TdlTestCatalogController::class, 'stats'])->name('lab.catalog.stats');
        Route::get('/category/{category}', [TdlTestCatalogController::class, 'byCategory'])->name('lab.catalog.category');
        Route::get('/{id}', [TdlTestCatalogController::class, 'show'])->name('lab.catalog.show');
        Route::post('/validate', [TdlTestCatalogController::class, 'validate'])->name('lab.catalog.validate');
    });

    // Lab Requests Routes (providers and clinic_admin only)
    Route::middleware(['role:provider|clinic_admin'])->prefix('lab/requests')->group(function () {
        Route::get('/', [TdlLabRequestController::class, 'index'])->name('lab.requests.index');
        Route::post('/', [TdlLabRequestController::class, 'store'])->name('lab.requests.store');
        Route::get('/stats', [TdlLabRequestController::class, 'stats'])->name('lab.requests.stats');
        Route::get('/status/{status}', [TdlLabRequestController::class, 'byStatus'])->name('lab.requests.status');
        Route::get('/patient/{patientId}', [TdlLabRequestController::class, 'patient'])->name('lab.requests.patient');
        Route::get('/{id}', [TdlLabRequestController::class, 'show'])->name('lab.requests.show');
        Route::post('/{id}/send', [TdlLabRequestController::class, 'send'])->name('lab.requests.send');
        Route::post('/{id}/cancel', [TdlLabRequestController::class, 'cancel'])->name('lab.requests.cancel');
        Route::post('/{id}/retry', [TdlLabRequestController::class, 'retry'])->name('lab.requests.retry');
    });

    // Lab Results Routes (providers and clinic_admin only)
    Route::middleware(['role:provider|clinic_admin'])->prefix('lab/results')->group(function () {
        Route::get('/', [TdlLabResultController::class, 'index'])->name('lab.results.index');
        Route::get('/stats', [TdlLabResultController::class, 'stats'])->name('lab.results.stats');
        Route::get('/unreviewed', [TdlLabResultController::class, 'unreviewed'])->name('lab.results.unreviewed');
        Route::get('/status/{status}', [TdlLabResultController::class, 'byStatus'])->name('lab.results.status');
        Route::get('/patient/{patientId}', [TdlLabResultController::class, 'patient'])->name('lab.results.patient');
        Route::post('/process', [TdlLabResultController::class, 'process'])->name('lab.results.process');
        Route::post('/archive', [TdlLabResultController::class, 'archive'])->name('lab.results.archive');
        Route::get('/{id}', [TdlLabResultController::class, 'show'])->name('lab.results.show');
        Route::post('/{id}/review', [TdlLabResultController::class, 'review'])->name('lab.results.review');
        Route::get('/{id}/download', [TdlLabResultController::class, 'download'])->name('lab.results.download');
    });

    // TDL Settings Routes (clinic_admin only)
    Route::middleware(['role:clinic_admin'])->prefix('lab/settings')->group(function () {
        Route::get('/', [TdlSettingsController::class, 'show'])->name('lab.settings.show');
        Route::post('/', [TdlSettingsController::class, 'update'])->name('lab.settings.update');
        Route::get('/status', [TdlSettingsController::class, 'status'])->name('lab.settings.status');
        Route::post('/test-connection', [TdlSettingsController::class, 'testConnection'])->name('lab.settings.test');
        Route::post('/test-azure', [TdlSettingsController::class, 'testAzureConnection'])->name('lab.settings.test-azure');
        Route::get('/azure-stats', [TdlSettingsController::class, 'azureStats'])->name('lab.settings.azure-stats');
        Route::post('/process-results', [TdlSettingsController::class, 'processResults'])->name('lab.settings.process-results');
        Route::post('/disable', [TdlSettingsController::class, 'disable'])->name('lab.settings.disable');
    });
});

// Drug Search Routes (RxNorm API Integration)
Route::middleware(['auth', 'verified'])->prefix('drugs')->group(function () {
    Route::get('/search', [DrugSearchController::class, 'search'])->name('drugs.search');
    Route::get('/enhanced-search', [DrugSearchController::class, 'enhancedSearch'])->name('drugs.enhanced-search');
    Route::get('/suggestions', [DrugSearchController::class, 'suggestions'])->name('drugs.suggestions');
    Route::get('/{rxcui}/details', [DrugSearchController::class, 'details'])->name('drugs.details');
    Route::get('/{rxcui}/classes', [DrugSearchController::class, 'drugClasses'])->name('drugs.classes');
    Route::post('/interactions', [DrugSearchController::class, 'interactions'])->name('drugs.interactions');
});

