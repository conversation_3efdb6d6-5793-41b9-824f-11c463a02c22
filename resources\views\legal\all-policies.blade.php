<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legal Policies - Medroid AI</title>
    <meta name="description" content="Access all legal policies, terms of service, privacy policy, and terms for Medroid AI platform">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.5;
            color: #1f2937;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
        }
        .container {
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 32px;
            border-bottom: 1px solid #e5e7eb;
        }
        .back-link {
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            margin-bottom: 16px;
            display: inline-block;
            transition: color 0.2s;
        }
        .back-link:hover {
            color: #374151;
        }
        h1 {
            color: #111827;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.025em;
        }
        .subtitle {
            color: #6b7280;
            margin: 8px 0 0 0;
            font-size: 16px;
            font-weight: 400;
        }
        .policies-list {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .policy-item {
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.15s ease;
        }
        .policy-item:last-child {
            border-bottom: none;
        }
        .policy-item:hover {
            background: #f9fafb;
        }
        .policy-link {
            display: block;
            padding: 24px 32px;
            text-decoration: none;
            color: inherit;
            transition: all 0.15s ease;
        }
        .policy-link:hover {
            color: inherit;
        }
        .policy-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .policy-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        .policy-icon {
            width: 32px;
            height: 32px;
            background: #f3f4f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #6b7280;
        }
        .policy-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }
        .footer {
            background: #f8fafc;
            padding: 24px 32px;
            border-top: 1px solid #e5e7eb;
        }
        .contact-info {
            text-align: center;
        }
        .contact-info h3 {
            color: #111827;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 12px 0;
        }
        .contact-info p {
            color: #6b7280;
            font-size: 14px;
            margin: 4px 0;
        }
        .contact-info a {
            color: #3b82f6;
            text-decoration: none;
        }
        .contact-info a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            body {
                padding: 12px;
            }
            .header {
                padding: 24px 20px;
            }
            .policy-link {
                padding: 20px;
            }
            .footer {
                padding: 20px;
            }
            h1 {
                font-size: 24px;
            }
            .subtitle {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-link">← Back to Medroid</a>
            <h1>Legal Policies</h1>
            <p class="subtitle">Access all our terms, policies, and legal agreements</p>
        </div>

        <ul class="policies-list">
            <li class="policy-item">
                <a href="{{ route('terms-of-service') }}" class="policy-link">
                    <div class="policy-header">
                        <h2 class="policy-title">Terms of Service</h2>
                        <div class="policy-icon">T</div>
                    </div>
                    <p class="policy-description">Comprehensive terms covering medical disclaimers, AI assistance limitations, user responsibilities, and legal agreements for using Medroid AI platform.</p>
                </a>
            </li>

            <li class="policy-item">
                <a href="{{ route('privacy-policy') }}" class="policy-link">
                    <div class="policy-header">
                        <h2 class="policy-title">Privacy Policy</h2>
                        <div class="policy-icon">P</div>
                    </div>
                    <p class="policy-description">How we collect, use, and protect your personal and health information. Our commitment to your privacy and data security.</p>
                </a>
            </li>

            <li class="policy-item">
                <a href="{{ route('terms-of-use-indian') }}" class="policy-link">
                    <div class="policy-header">
                        <h2 class="policy-title">Terms of Use (Indian Users)</h2>
                        <div class="policy-icon">I</div>
                    </div>
                    <p class="policy-description">Specific terms applicable to users in India, including compliance with local regulations and grievance redressal mechanisms.</p>
                </a>
            </li>

            <li class="policy-item">
                <a href="{{ route('terms-conditions-sale') }}" class="policy-link">
                    <div class="policy-header">
                        <h2 class="policy-title">Terms & Conditions of Sale (US/UK)</h2>
                        <div class="policy-icon">S</div>
                    </div>
                    <p class="policy-description">Terms governing sales and purchases for users in the United States and United Kingdom, including payment terms and refund policies.</p>
                </a>
            </li>
        </ul>

        <div class="footer">
            <div class="contact-info">
                <h3>Need Help or Have Questions?</h3>
                <p>For all inquiries including legal, privacy, and general support:</p>
                <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p style="margin-top: 12px; font-size: 13px;">Last updated: {{ date('F j, Y') }}</p>
            </div>
        </div>
    </div>
</body>
</html>
