<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, router } from '@inertiajs/vue3'
import { ref, reactive, onMounted } from 'vue'
import { useConsultationApi, type Consultation } from '@/composables/useConsultationApi'
import PatientSelect from '@/components/ui/PatientSelect.vue'
import { useNotifications } from '@/composables/useNotifications'
import Icon from '@/components/Icon.vue'
import AppointmentViewModal from '@/components/AppointmentViewModal.vue'
import axios from 'axios'

const { consultations, loading, error, getConsultations, createConsultation: apiCreateConsultation, deleteConsultation, archiveConsultation: apiArchiveConsultation } = useConsultationApi()
const { showSuccess, showError, showDeleteConfirm, notifyOperation } = useNotifications()

const breadcrumbs = [
    { title: 'Dashboard', href: '/dashboard' },
    { title: 'Consultations', href: '/consultations' },
]

const showCreateForm = ref(false)
const isCreating = ref(false)
const formErrors = ref<Record<string, string>>({})
const sharingConsultationId = ref<number | null>(null)

// Appointment modal state
const showAppointmentModal = ref(false)
const selectedAppointment = ref(null)

// Archive functionality
const archivingConsultationId = ref<number | null>(null)
const showArchivedFilter = ref(false)

const consultationForm = reactive({
    patient_id: null as number | null,
    consultation_type: 'general',
    consultation_date: new Date().toISOString().slice(0, 10),
    consultation_mode: 'in_person',
    chief_complaint: '',
    is_telemedicine: false
})

const getStatusBadgeClass = (status: string) => {
    const classes = {
        'draft': 'bg-gray-100 text-gray-800',
        'in_progress': 'bg-yellow-100 text-yellow-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-red-100 text-red-800'
    }
    return classes[status] || 'bg-gray-100 text-gray-800'
}

const onPatientSelected = (patient: any) => {
    consultationForm.patient_id = patient.id
    // Clear any previous patient_id error
    if (formErrors.value.patient_id) {
        delete formErrors.value.patient_id
    }
}

const validateForm = () => {
    formErrors.value = {}

    if (!consultationForm.patient_id) {
        formErrors.value.patient_id = 'Please select a patient'
    }

    if (!consultationForm.consultation_type) {
        formErrors.value.consultation_type = 'Please select a consultation type'
    }

    if (!consultationForm.consultation_date) {
        formErrors.value.consultation_date = 'Please select a date'
    }

    return Object.keys(formErrors.value).length === 0
}

const createConsultation = async () => {
    if (!validateForm()) {
        return
    }

    try {
        isCreating.value = true

        // Set is_telemedicine based on consultation_mode
        consultationForm.is_telemedicine = consultationForm.consultation_mode === 'video' || consultationForm.consultation_mode === 'phone'

        const response = await apiCreateConsultation(consultationForm)
        if (response?.data) {
            showCreateForm.value = false
            // Reset form
            Object.assign(consultationForm, {
                patient_id: null,
                consultation_type: 'general',
                consultation_date: new Date().toISOString().slice(0, 10),
                consultation_mode: 'in_person',
                chief_complaint: '',
                is_telemedicine: false
            })

            // Show success message
            alert(`Consultation created successfully for ${response.data.patient?.user?.name || 'patient'}`)

            // Reload consultations to show the new one
            await loadConsultations()
        }
    } catch (err: any) {
        console.error('Error creating consultation:', err)

        // Handle validation errors from the server
        if (err.response?.status === 422 && err.response?.data?.errors) {
            formErrors.value = err.response.data.errors
            showError('Please fix the validation errors and try again.')
        } else if (err.response?.status === 403) {
            showError('You do not have permission to create consultations.')
        } else if (err.response?.status === 404) {
            showError('Patient not found. Please select a valid patient.')
        } else {
            const errorMessage = err.response?.data?.message || 'Failed to create consultation. Please try again.'
            showError(errorMessage)
        }
    } finally {
        isCreating.value = false
    }
}

const loadConsultations = async () => {
    await getConsultations()
}

const viewConsultation = (consultation: Consultation) => {
    router.visit(`/consultation-dashboard/${consultation.patient_id}?consultation_id=${consultation.id}`)
}

const editConsultation = (id: number) => {
    router.visit(`/consultations/${id}/edit`)
}

const handleDeleteConsultation = async (consultation: Consultation) => {
    const consultationInfo = `${consultation.patient?.user?.name || 'Unknown Patient'} - ${consultation.consultation_type || 'General'}`

    if (await showDeleteConfirm(consultationInfo, 'consultation')) {
        try {
            const success = await deleteConsultation(consultation.id)
            if (success) {
                showSuccess('Consultation deleted successfully')
                await loadConsultations()
            }
        } catch (error: any) {
            console.error('Error deleting consultation:', error)

            // Show specific error message from backend if available
            const errorMessage = error.response?.data?.message || 'Failed to delete consultation. Please try again.'
            showError(errorMessage)
        }
    }
}

const shareWithPatient = async (consultation: Consultation) => {
    const patientEmail = consultation.patient?.user?.email
    if (!patientEmail) {
        showError('Patient email not found')
        return
    }

    // Set loading state
    sharingConsultationId.value = consultation.id

    try {
        await notifyOperation(
            () => axios.post(`/consultations/${consultation.id}/send-summary`, {
                patient_email: patientEmail
            }),
            {
                loading: 'Sending consultation summary...',
                success: `Consultation summary sent to ${patientEmail}`,
                error: 'Failed to send consultation summary. Please try again.'
            }
        )
    } catch (error: any) {
        console.error('Error sharing consultation:', error)
    } finally {
        // Clear loading state
        sharingConsultationId.value = null
    }
}

// Appointment-related functions
const viewAppointmentDetails = async (consultation: Consultation) => {
    if (!consultation.appointment_id) {
        showError('No appointment linked to this consultation')
        return
    }

    try {
        // Fetch appointment details
        const response = await axios.get(`/api/appointments/${consultation.appointment_id}`)
        selectedAppointment.value = response.data.data
        showAppointmentModal.value = true
    } catch (error: any) {
        console.error('Error fetching appointment details:', error)
        showError('Failed to load appointment details. Please try again.')
    }
}

const closeAppointmentModal = () => {
    showAppointmentModal.value = false
    selectedAppointment.value = null
}

// Archive functionality
const archiveConsultation = async (consultation: Consultation) => {
    const consultationInfo = `${consultation.patient?.user?.name || 'Unknown Patient'} - ${consultation.consultation_type || 'General'}`

    if (await showDeleteConfirm(consultationInfo, 'consultation', 'Archive')) {
        try {
            archivingConsultationId.value = consultation.id

            await apiArchiveConsultation(consultation.id)

            showSuccess('Consultation archived successfully')
            await loadConsultations()
        } catch (error: any) {
            console.error('Error archiving consultation:', error)
            const errorMessage = error.response?.data?.message || 'Failed to archive consultation. Please try again.'
            showError(errorMessage)
        } finally {
            archivingConsultationId.value = null
        }
    }
}

onMounted(() => {
    loadConsultations()
})
</script>

<template>
    <AppLayout>
        <Head title="Consultations" />
        
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 text-gray-900">
                        <div class="flex justify-between items-center">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                                    <Icon name="list" class="mr-3 text-blue-600 w-6 h-6" />
                                    Patient Consultations
                                </h2>
                                <p class="text-gray-600">Manage your patient consultations</p>
                            </div>
                            <div class="flex space-x-3">
                                <button
                                   @click="showCreateForm = true"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2 transition-colors duration-200">
                                    <Icon name="plus" class="w-4 h-4" />
                                    <span>Add Consultation</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Consultations List -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900">
                        <div v-if="loading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                            <p class="mt-2 text-gray-600">Loading consultations...</p>
                        </div>

                        <div v-else-if="error" class="text-center py-8">
                            <p class="text-red-600">{{ error }}</p>
                        </div>

                        <div v-else-if="consultations.length === 0" class="text-center py-8">
                            <div class="text-gray-400 mb-4">
                                <Icon name="stethoscope" class="w-16 h-16 mx-auto" />
                            </div>
                            <p class="text-gray-500">No consultations found</p>
                            <button
                               @click="showCreateForm = true"
                               class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                Create your first consultation
                            </button>
                        </div>

                        <div v-else class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="user" class="w-3 h-3 mr-2 inline" />Patient Name
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="calendar" class="w-3 h-3 mr-2 inline" />Date
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="stethoscope" class="w-3 h-3 mr-2 inline" />Type
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="calendar-check" class="w-3 h-3 mr-2 inline" />Appointment
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="info" class="w-3 h-3 mr-2 inline" />Status
                                        </th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <Icon name="settings" class="w-3 h-3 mr-2 inline" />Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="consultation in consultations" :key="consultation.id" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ consultation.patient?.user?.name || 'N/A' }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ consultation.patient?.user?.email || 'N/A' }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ new Date(consultation.consultation_date).toLocaleDateString() }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ consultation.consultation_type || 'General' }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div v-if="consultation.appointment_id" class="flex items-center space-x-2">
                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                                    <Icon name="calendar-check" class="w-3 h-3 mr-1" />
                                                    Linked
                                                </span>
                                                <button
                                                    @click="viewAppointmentDetails(consultation)"
                                                    class="text-blue-600 hover:text-blue-800 text-xs underline"
                                                    title="View Appointment Details"
                                                >
                                                    View
                                                </button>
                                            </div>
                                            <span v-else class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 border border-gray-200">
                                                <Icon name="calendar-x" class="w-3 h-3 mr-1" />
                                                No Appointment
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span :class="getStatusBadgeClass(consultation.status)"
                                                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                                {{ consultation.status || 'Draft' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center justify-center space-x-3">
                                                <!-- View Button -->
                                                <button
                                                    @click="viewAppointmentDetails(consultation)"
                                                    class="text-blue-600 hover:text-blue-800 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                                                    title="View Appointment Details"
                                                    v-if="consultation.appointment_id"
                                                >
                                                    <Icon name="eye" class="w-4 h-4" />
                                                </button>

                                                <!-- View Consultation Button (for consultations without appointments) -->
                                                <button
                                                    @click="viewConsultation(consultation)"
                                                    class="text-blue-600 hover:text-blue-800 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50"
                                                    title="View Consultation"
                                                    v-else
                                                >
                                                    <Icon name="eye" class="w-4 h-4" />
                                                </button>

                                                <!-- Edit Button -->
                                                <button
                                                    @click="editConsultation(consultation.id)"
                                                    class="text-green-600 hover:text-green-800 transition-colors duration-200 p-2 rounded-full hover:bg-green-50"
                                                    v-if="consultation.status !== 'completed'"
                                                    title="Edit Consultation"
                                                >
                                                    <Icon name="edit" class="w-4 h-4" />
                                                </button>

                                                <!-- Share with Patient Button -->
                                                <button
                                                    @click="shareWithPatient(consultation)"
                                                    :disabled="sharingConsultationId === consultation.id"
                                                    class="text-blue-600 hover:text-blue-800 transition-colors duration-200 p-2 rounded-full hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    v-if="consultation.status === 'completed' && consultation.patient?.user?.email"
                                                    title="Share Summary with Patient"
                                                >
                                                    <Icon v-if="sharingConsultationId === consultation.id" name="loader-2" class="w-4 h-4 animate-spin" />
                                                    <Icon v-else name="share" class="w-4 h-4" />
                                                </button>

                                                <!-- Archive Button -->
                                                <button
                                                    @click="archiveConsultation(consultation)"
                                                    :disabled="archivingConsultationId === consultation.id"
                                                    class="text-orange-600 hover:text-orange-800 transition-colors duration-200 p-2 rounded-full hover:bg-orange-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                                    v-if="consultation.status === 'completed'"
                                                    title="Archive Consultation"
                                                >
                                                    <Icon v-if="archivingConsultationId === consultation.id" name="loader-2" class="w-4 h-4 animate-spin" />
                                                    <Icon v-else name="archive" class="w-4 h-4" />
                                                </button>

                                                <!-- Delete Button -->
                                                <button
                                                    @click="handleDeleteConsultation(consultation)"
                                                    class="text-red-600 hover:text-red-800 transition-colors duration-200 p-2 rounded-full hover:bg-red-50"
                                                    v-if="!consultation.status || consultation.status !== 'completed'"
                                                    :title="`Delete Consultation (Status: ${consultation.status || 'Draft'})`"
                                                >
                                                    <Icon name="trash-2" class="w-4 h-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Create Consultation Modal -->
                <div v-if="showCreateForm" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">New Consultation</h3>
                                <button @click="showCreateForm = false" class="text-gray-400 hover:text-gray-600">
                                    <span class="sr-only">Close</span>
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Patient *</label>
                                    <PatientSelect
                                        v-model="consultationForm.patient_id"
                                        placeholder="Search for a patient..."
                                        required
                                        @patient-selected="onPatientSelected"
                                    />
                                    <p v-if="formErrors.patient_id" class="text-red-500 text-sm mt-1">{{ formErrors.patient_id }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Consultation Type</label>
                                    <select
                                        v-model="consultationForm.consultation_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="general">General Consultation</option>
                                        <option value="follow_up">Follow-up</option>
                                        <option value="emergency">Emergency</option>
                                        <option value="specialist">Specialist Consultation</option>
                                        <option value="routine_checkup">Routine Checkup</option>
                                    </select>
                                    <p v-if="formErrors.consultation_type" class="text-red-500 text-sm mt-1">{{ formErrors.consultation_type }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                                    <input
                                        v-model="consultationForm.consultation_date"
                                        type="date"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required
                                    >
                                    <p v-if="formErrors.consultation_date" class="text-red-500 text-sm mt-1">{{ formErrors.consultation_date }}</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Consultation Mode</label>
                                    <select
                                        v-model="consultationForm.consultation_mode"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="in_person">In Person</option>
                                        <option value="video">Video Call</option>
                                        <option value="phone">Phone Call</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Chief Complaint (Optional)</label>
                                    <textarea
                                        v-model="consultationForm.chief_complaint"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Brief description of the patient's main concern..."
                                        rows="3"
                                    ></textarea>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 mt-6">
                                <button
                                    @click="showCreateForm = false"
                                    :disabled="isCreating"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md disabled:opacity-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    @click="createConsultation"
                                    :disabled="isCreating || !consultationForm.patient_id"
                                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {{ isCreating ? 'Creating...' : 'Create Consultation' }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Appointment Details Modal -->
        <AppointmentViewModal
            :is-open="showAppointmentModal"
            :appointment="selectedAppointment"
            :show-edit-button="false"
            @close="closeAppointmentModal"
        />
    </AppLayout>
</template>
