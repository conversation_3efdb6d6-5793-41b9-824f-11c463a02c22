<script setup lang="ts">
import { ref, defineEmits, defineProps, nextTick, watch, onMounted, onUnmounted } from 'vue';

interface ChatMode {
    id: string;
    name: string;
    description: string;
    icon: string;
    color: string;
}

interface Props {
    modelValue: string;
    placeholder?: string;
    disabled?: boolean;
    isLoading?: boolean;
    showTools?: boolean;
    showVersion?: boolean;
    selectedMode?: string;
    searchEnabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Type your message...',
    disabled: false,
    isLoading: false,
    showTools: false, // Default to false to remove tools
    showVersion: false,
    selectedMode: 'consultation',
    searchEnabled: true
});

const emit = defineEmits<{
    'update:modelValue': [value: string];
    'send': [];
    'keydown': [event: KeyboardEvent];
    'mode-change': [mode: string];
    'search-toggle': [enabled: boolean];
}>();

const chatInputRef = ref<HTMLTextAreaElement | null>(null);
const showModeDropdown = ref(false);
const dropdownPosition = ref({ top: 0, left: 0 });
const isExpanded = ref(false);
const isDisclaimerExpanded = ref(false);

// Available chat modes - Only consultation and maya
const chatModes: ChatMode[] = [
    {
        id: 'consultation',
        name: 'AI Doctor',
        description: 'Medical consultation & diagnosis',
        icon: 'medical',
        color: 'teal'
    },
    {
        id: 'maya',
        name: 'Maya',
        description: 'Health & wellness expert',
        icon: 'wellness',
        color: 'coral'
    }
];

// Get current mode
const getCurrentMode = () => {
    return chatModes.find(mode => mode.id === props.selectedMode) || chatModes[0];
};

// Handle mode selection
const selectMode = (mode: ChatMode) => {
    emit('mode-change', mode.id);
    showModeDropdown.value = false;
};

// Handle search toggle
const toggleSearch = () => {
    emit('search-toggle', !props.searchEnabled);
};

// Toggle dropdown
const toggleModeDropdown = () => {
    if (!showModeDropdown.value) {
        // Calculate position before showing
        calculateDropdownPosition();
    }
    showModeDropdown.value = !showModeDropdown.value;
    console.log('Dropdown toggled:', showModeDropdown.value);
    console.log('Available modes:', chatModes.length);
};

// Calculate dropdown position
const calculateDropdownPosition = () => {
    const button = document.querySelector('.mode-button');
    if (button) {
        const rect = button.getBoundingClientRect();
        dropdownPosition.value = {
            top: rect.bottom + 8, // 8px gap
            left: rect.left
        };
        console.log('Dropdown position calculated:', dropdownPosition.value);
    }
};

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.mode-dropdown');
    const button = document.querySelector('.mode-button');

    if (dropdown && button && !dropdown.contains(target) && !button.contains(target)) {
        showModeDropdown.value = false;
    }
};

// Lifecycle hooks
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    window.addEventListener('resize', updateMobileState);
    // Initialize mobile state
    updateMobileState();
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
    window.removeEventListener('resize', updateMobileState);
});

// Format text to sentence case
const formatToSentenceCase = (text: string): string => {
    if (!text) return text;

    // Split by sentences (periods, exclamation marks, question marks)
    return text.replace(/([.!?]\s*)([a-z])/g, (_, punctuation, letter) => {
        return punctuation + letter.toUpperCase();
    }).replace(/^[a-z]/, (match) => {
        return match.toUpperCase();
    });
};

// Check if mobile
const isMobile = ref(false);
const updateMobileState = () => {
    isMobile.value = window.innerWidth < 640;
};

// Toggle expanded state
const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value;
    if (chatInputRef.value) {
        if (isExpanded.value) {
            autoResize();
        } else {
            chatInputRef.value.style.height = '40px';
            chatInputRef.value.style.overflowY = 'hidden';
        }
    }
};

// Toggle disclaimer expanded state
const toggleDisclaimer = () => {
    isDisclaimerExpanded.value = !isDisclaimerExpanded.value;
};

// Auto-resize textarea
const autoResize = () => {
    if (chatInputRef.value) {
        chatInputRef.value.style.height = 'auto';
        const scrollHeight = chatInputRef.value.scrollHeight;
        const maxHeight = 120; // Define maxHeight at function scope
        const minHeight = 40;

        if (isExpanded.value) {
            // When expanded, allow up to 4-5 lines
            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
            chatInputRef.value.style.height = `${newHeight}px`;
            chatInputRef.value.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden';
        } else {
            // When collapsed, keep single line only
            chatInputRef.value.style.height = '40px';
            chatInputRef.value.style.overflowY = 'hidden';
        }
    }
};

const updateValue = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    const formattedValue = formatToSentenceCase(target.value);
    emit('update:modelValue', formattedValue);
    
    // Auto-resize after update
    nextTick(() => {
        autoResize();
    });
};

const handleKeyDown = (event: KeyboardEvent) => {
    emit('keydown', event);
};

const handleSend = () => {
    emit('send');
    // Reset height after sending
    nextTick(() => {
        if (chatInputRef.value) {
            chatInputRef.value.style.height = '40px'; // Reset to smaller single line height
            chatInputRef.value.style.overflowY = 'hidden';
        }
    });
};

const focus = () => {
    if (chatInputRef.value) {
        chatInputRef.value.focus();
    }
};

// Watch for modelValue changes to handle auto-resize
watch(() => props.modelValue, () => {
    nextTick(() => {
        autoResize();
    });
});

// Expose focus method to parent
defineExpose({
    focus
});
</script>

<template>
    <div class="space-y-3">
        <div class="relative">
            <!-- Input Container - White background with seamless blending -->
            <div class="bg-white border border-gray-200 rounded-2xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
                <!-- Mode Selection Bar -->
                <div class="flex items-center justify-between px-4 py-2 border-b border-gray-100 bg-gray-50/30">
                    <div class="flex items-center space-x-3">
                        <div class="relative">
                            <!-- Current Mode Button -->
                            <button
                                @click="toggleModeDropdown"
                                class="mode-button flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500/20 focus:border-teal-500"
                            >
                            <!-- Mode Icon -->
                            <div class="w-5 h-5 flex items-center justify-center">
                                <!-- Medical Icon -->
                                <svg v-if="getCurrentMode().icon === 'medical'" class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4" />
                                </svg>
                                <!-- Wellness Icon -->
                                <svg v-else class="w-4 h-4 text-coral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                            </div>

                            <!-- Mode Name -->
                            <span>{{ getCurrentMode().name }}</span>

                            <!-- Dropdown Arrow -->
                            <svg
                                class="w-3 h-3 text-gray-400 transition-transform duration-200"
                                :class="{ 'rotate-180': showModeDropdown }"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Mode Dropdown -->
                        <div
                            v-if="showModeDropdown"
                            class="mode-dropdown fixed bg-white border border-gray-200 rounded-lg shadow-lg z-[9999] min-w-[200px]"
                            :style="{
                                top: dropdownPosition.top + 'px',
                                left: dropdownPosition.left + 'px'
                            }"
                        >
                            <div class="py-1">
                                <div
                                    v-for="mode in chatModes"
                                    :key="mode.id"
                                    @click="selectMode(mode)"
                                    class="flex items-center space-x-3 px-3 py-2 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                                    :class="{ 'bg-blue-50 text-blue-700': mode.id === props.selectedMode }"
                                >
                                    <!-- Mode Icon -->
                                    <div class="w-4 h-4 flex items-center justify-center">
                                        <!-- Medical Icon -->
                                        <svg v-if="mode.icon === 'medical'" class="w-4 h-4" :class="mode.id === props.selectedMode ? 'text-blue-600' : 'text-teal-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <!-- Wellness Icon -->
                                        <svg v-else class="w-4 h-4" :class="mode.id === props.selectedMode ? 'text-blue-600' : 'text-coral-600'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                                        </svg>
                                    </div>

                                    <!-- Mode Content -->
                                    <div class="flex-1">
                                        <div class="text-sm font-medium" :class="mode.id === props.selectedMode ? 'text-blue-700' : 'text-gray-900'">
                                            {{ mode.name }}
                                        </div>
                                        <div class="text-xs text-gray-500">{{ mode.description }}</div>
                                    </div>

                                    <!-- Selection Indicator -->
                                    <div v-if="mode.id === props.selectedMode" class="w-4 h-4">
                                        <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- Search Toggle (Only for Maya mode) - Positioned beside mode selector -->
                        <div v-if="getCurrentMode().id === 'maya'" class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">Search:</span>
                            <button
                                @click="toggleSearch"
                                :class="[
                                    'relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-teal-500/20',
                                    searchEnabled ? 'bg-teal-500' : 'bg-gray-300'
                                ]"
                            >
                                <span
                                    :class="[
                                        'inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200',
                                        searchEnabled ? 'translate-x-5' : 'translate-x-1'
                                    ]"
                                />
                            </button>
                        </div>
                    </div>

                    <!-- Mode Info -->
                    <div class="text-xs text-gray-500 max-w-xs truncate">
                        {{ getCurrentMode().description }}
                    </div>
                </div>

                <!-- Input Area -->
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        :value="modelValue"
                        @input="updateValue"
                        @keydown="handleKeyDown"
                        :placeholder="placeholder"
                        class="w-full px-4 py-3 text-gray-800 placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 text-base leading-relaxed transition-all duration-200 break-words"
                        style="height: 40px; overflow-y: hidden; word-wrap: break-word; white-space: pre-wrap;"
                        :disabled="disabled || isLoading"
                    ></textarea>

                    <!-- Bottom Bar - Simplified -->
                    <div class="flex items-center justify-between px-4 pb-3">
                        <!-- Expand/Collapse Button -->
                        <button
                            @click="toggleExpanded"
                            class="text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200 flex items-center space-x-1"
                        >
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path v-if="!isExpanded" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8V20m0 0l4-4m-4 4l-4-4M7 16V4m0 0L3 8m4-4l4 4" />
                            </svg>
                            <span>{{ isExpanded ? 'Less' : 'More' }}</span>
                        </button>

                        <!-- Send Button -->
                        <button
                            @click="handleSend"
                            :disabled="isLoading || !modelValue.trim()"
                            :class="[
                                'p-2 rounded-lg transition-all duration-200 transform hover:scale-105',
                                modelValue.trim() && !isLoading
                                    ? 'bg-teal-500 text-white hover:bg-teal-600 shadow-lg'
                                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                            ]"
                        >
                            <svg v-if="!isLoading" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" />
                            </svg>
                            <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Disclaimer - Responsive with Mobile Collapse -->
        <div class="relative">
            <!-- Mobile: Collapsible version -->
            <div v-if="isMobile" class="text-xs text-gray-500 text-center px-4 py-2 bg-gray-50/60 rounded-lg border border-gray-200/50">
                <div v-if="!isDisclaimerExpanded" class="flex items-center justify-center space-x-2">
                    <span class="truncate">Medroid is an AI tool, not a doctor...</span>
                    <button 
                        @click="toggleDisclaimer"
                        class="text-gray-400 hover:text-gray-600 transition-colors duration-200 flex-shrink-0"
                    >
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>
                <div v-else class="space-y-2">
                    <div class="flex items-start justify-between">
                        <p class="leading-relaxed text-left flex-1">
                            Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our
                            <a href="https://medroid.ai/terms-of-service-1/" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Terms of Service</a> and
                            <a href="https://medroid.ai/privacy-policy/" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Privacy Policy</a>.
                        </p>
                        <button 
                            @click="toggleDisclaimer"
                            class="text-gray-400 hover:text-gray-600 transition-colors duration-200 ml-2 flex-shrink-0"
                        >
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Desktop: Full version -->
            <p v-else class="text-xs text-gray-500 text-center leading-relaxed px-4 py-2 bg-gray-50/60 rounded-lg border border-gray-200/50">
                Medroid is an AI tool, not a doctor. Always consult a healthcare professional. By using Medroid, you agree to our
                <a href="/policies" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Terms of Service</a> and
                <a href="/policies" target="_blank" class="text-gray-600 hover:text-gray-800 underline underline-offset-2 transition-colors duration-150">Privacy Policy</a>.
            </p>
        </div>
    </div>
</template>

<style scoped>
/* Brand Colors */
.text-teal-600 {
    color: #0d9488;
}

.text-coral-600 {
    color: #ea580c;
}

/* Smooth transitions for textarea resize */
textarea {
    transition: height 0.2s ease-out;
}

/* Custom scrollbar for textarea when needed */
textarea::-webkit-scrollbar {
    width: 4px;
}

textarea::-webkit-scrollbar-track {
    background: transparent;
}

textarea::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

/* Firefox scrollbar */
textarea {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Dropdown animation */
.mode-dropdown {
    animation: dropdownSlideIn 0.2s ease-out;
}

@keyframes dropdownSlideIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>